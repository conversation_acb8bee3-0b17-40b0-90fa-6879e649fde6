#!/usr/bin/env python3
"""
Training script for fine-tuning a small language model with LoRA/QLoRA
on command-line Q&A dataset.

Model: TinyLlama-1.1B or Phi-2 (≤2B parameters)
Method: LoRA/QLoRA for efficient fine-tuning
Duration: 1 epoch as per requirements
"""

import os
import json
import torch
import argparse
from datetime import datetime
from pathlib import Path

from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from datasets import Dataset
from peft import LoraConfig, get_peft_model, TaskType
import wandb

def load_dataset(data_path: str):
    """Load and preprocess the command-line Q&A dataset."""
    print(f"Loading dataset from {data_path}")
    
    with open(data_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    qa_pairs = data['data']
    print(f"Loaded {len(qa_pairs)} Q&A pairs")
    
    # Format data for instruction tuning
    formatted_data = []
    for pair in qa_pairs:
        # Create instruction-following format
        instruction = f"Question: {pair['question']}\nAnswer:"
        response = f" {pair['answer']}"
        
        # Combine for causal language modeling
        text = instruction + response
        formatted_data.append({"text": text})
    
    return Dataset.from_list(formatted_data)

def tokenize_function(examples, tokenizer, max_length=512):
    """Tokenize the dataset."""
    return tokenizer(
        examples["text"],
        truncation=True,
        padding=False,
        max_length=max_length,
        return_overflowing_tokens=False,
    )

def setup_model_and_tokenizer(model_name: str, use_4bit: bool = True):
    """Setup model and tokenizer with optional 4-bit quantization."""
    print(f"Loading model: {model_name}")
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Load model with optional quantization
    if use_4bit:
        from transformers import BitsAndBytesConfig
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=True,
        )
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            quantization_config=bnb_config,
            device_map="auto",
            trust_remote_code=True
        )
    else:
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True
        )
    
    return model, tokenizer

def setup_lora_config():
    """Setup LoRA configuration."""
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        inference_mode=False,
        r=16,  # LoRA rank
        lora_alpha=32,  # LoRA scaling parameter
        lora_dropout=0.1,
        target_modules=["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
    )
    return lora_config

def main():
    parser = argparse.ArgumentParser(description="Fine-tune language model on command-line Q&A")
    parser.add_argument("--model_name", default="TinyLlama/TinyLlama-1.1B-Chat-v1.0", 
                       help="Model name or path")
    parser.add_argument("--data_path", default="../data/command_line_qa.json",
                       help="Path to Q&A dataset")
    parser.add_argument("--output_dir", default="./adapters",
                       help="Output directory for LoRA adapters")
    parser.add_argument("--use_4bit", action="store_true", default=True,
                       help="Use 4-bit quantization")
    parser.add_argument("--max_length", type=int, default=512,
                       help="Maximum sequence length")
    parser.add_argument("--batch_size", type=int, default=4,
                       help="Training batch size")
    parser.add_argument("--learning_rate", type=float, default=2e-4,
                       help="Learning rate")
    parser.add_argument("--num_epochs", type=int, default=1,
                       help="Number of training epochs")
    parser.add_argument("--use_wandb", action="store_true",
                       help="Use Weights & Biases for logging")
    
    args = parser.parse_args()
    
    # Initialize wandb if requested
    if args.use_wandb:
        wandb.init(
            project="command-line-assistant",
            config=vars(args)
        )
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load dataset
    dataset = load_dataset(args.data_path)
    
    # Setup model and tokenizer
    model, tokenizer = setup_model_and_tokenizer(args.model_name, args.use_4bit)
    
    # Tokenize dataset
    tokenized_dataset = dataset.map(
        lambda x: tokenize_function(x, tokenizer, args.max_length),
        batched=True,
        remove_columns=dataset.column_names
    )
    
    # Setup LoRA
    lora_config = setup_lora_config()
    model = get_peft_model(model, lora_config)
    
    print(f"Trainable parameters: {model.num_parameters()}")
    print(f"Total parameters: {model.num_parameters(only_trainable=False)}")
    
    # Training arguments
    training_args = TrainingArguments(
        output_dir=args.output_dir,
        num_train_epochs=args.num_epochs,
        per_device_train_batch_size=args.batch_size,
        gradient_accumulation_steps=4,
        warmup_steps=100,
        learning_rate=args.learning_rate,
        fp16=True,
        logging_steps=10,
        save_strategy="epoch",
        evaluation_strategy="no",
        save_total_limit=1,
        remove_unused_columns=False,
        report_to="wandb" if args.use_wandb else None,
    )
    
    # Data collator
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )
    
    # Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_dataset,
        data_collator=data_collator,
    )
    
    # Train
    print("Starting training...")
    start_time = datetime.now()
    trainer.train()
    end_time = datetime.now()
    
    training_time = end_time - start_time
    print(f"Training completed in: {training_time}")
    
    # Save the LoRA adapter
    model.save_pretrained(args.output_dir)
    tokenizer.save_pretrained(args.output_dir)
    
    # Save training metadata
    metadata = {
        "model_name": args.model_name,
        "training_time": str(training_time),
        "num_epochs": args.num_epochs,
        "batch_size": args.batch_size,
        "learning_rate": args.learning_rate,
        "dataset_size": len(dataset),
        "max_length": args.max_length,
        "lora_config": {
            "r": lora_config.r,
            "lora_alpha": lora_config.lora_alpha,
            "lora_dropout": lora_config.lora_dropout,
        }
    }
    
    with open(os.path.join(args.output_dir, "training_metadata.json"), "w") as f:
        json.dump(metadata, f, indent=2)
    
    print(f"Model and adapters saved to: {args.output_dir}")
    print("Training completed successfully!")

if __name__ == "__main__":
    main()
