#!/usr/bin/env python3
"""
Quick training script for fine-tuning with LoRA on CPU/small GPU.
This is a simplified version that can work with limited resources.
"""

import os
import json
import torch
from datetime import datetime
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from datasets import Dataset
from peft import LoraConfig, get_peft_model, TaskType

def load_dataset(data_path: str):
    """Load and preprocess the command-line Q&A dataset."""
    print(f"Loading dataset from {data_path}")
    
    with open(data_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    qa_pairs = data['data']
    print(f"Loaded {len(qa_pairs)} Q&A pairs")
    
    # Format data for instruction tuning - simplified format
    formatted_data = []
    for pair in qa_pairs[:50]:  # Use only first 50 for quick training
        # Create simple instruction-following format
        text = f"Q: {pair['question']}\nA: {pair['answer']}"
        formatted_data.append({"text": text})
    
    print(f"Using {len(formatted_data)} samples for quick training")
    return Dataset.from_list(formatted_data)

def tokenize_function(examples, tokenizer, max_length=256):
    """Tokenize the dataset with shorter sequences."""
    return tokenizer(
        examples["text"],
        truncation=True,
        padding="max_length",
        max_length=max_length,
        return_tensors="pt"
    )

def main():
    print("🚀 Quick LoRA Fine-tuning for Command-Line Assistant")
    print("=" * 60)
    
    # Configuration
    MODEL_NAME = "microsoft/DialoGPT-small"  # Smaller model for quick training
    DATA_PATH = "data/command_line_qa.json"
    OUTPUT_DIR = "training/adapters"
    MAX_LENGTH = 256
    
    print(f"Model: {MODEL_NAME}")
    print(f"Output: {OUTPUT_DIR}")
    
    # Create output directory
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Load dataset
    print("\n📊 Loading dataset...")
    dataset = load_dataset(DATA_PATH)
    
    # Load model and tokenizer
    print("\n🤖 Loading model and tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
    
    # Add pad token if missing
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    model = AutoModelForCausalLM.from_pretrained(
        MODEL_NAME,
        torch_dtype=torch.float32,  # Use float32 for CPU compatibility
        device_map="auto"
    )
    
    print(f"Model parameters: {model.num_parameters():,}")
    
    # Tokenize dataset
    print("\n🔤 Tokenizing dataset...")
    tokenized_dataset = dataset.map(
        lambda x: tokenize_function(x, tokenizer, MAX_LENGTH),
        batched=True,
        remove_columns=dataset.column_names
    )
    
    # Setup LoRA
    print("\n⚙️ Setting up LoRA...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        inference_mode=False,
        r=8,  # Smaller rank for quick training
        lora_alpha=16,
        lora_dropout=0.1,
        target_modules=["c_attn", "c_proj"]  # DialoGPT specific modules
    )
    
    model = get_peft_model(model, lora_config)
    
    trainable_params = model.num_parameters()
    total_params = model.num_parameters(only_trainable=False)
    
    print(f"Trainable parameters: {trainable_params:,}")
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable %: {100 * trainable_params / total_params:.2f}%")
    
    # Training arguments - optimized for quick training
    print("\n🏋️ Setting up training...")
    training_args = TrainingArguments(
        output_dir=OUTPUT_DIR,
        num_train_epochs=1,  # As required
        per_device_train_batch_size=2,  # Small batch size
        gradient_accumulation_steps=2,
        warmup_steps=10,
        learning_rate=5e-4,  # Higher learning rate for quick training
        logging_steps=5,
        save_strategy="epoch",
        eval_strategy="no",
        save_total_limit=1,
        remove_unused_columns=False,
        dataloader_pin_memory=False,
        fp16=False,  # Disable for CPU compatibility
        report_to=None,  # Disable wandb
    )
    
    # Data collator
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )
    
    # Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_dataset,
        data_collator=data_collator,
    )
    
    # Train
    print("\n🎯 Starting training...")
    start_time = datetime.now()
    
    try:
        trainer.train()
        training_success = True
    except Exception as e:
        print(f"Training error: {e}")
        training_success = False
    
    end_time = datetime.now()
    training_time = end_time - start_time
    
    print(f"\n⏱️ Training completed in: {training_time}")
    
    if training_success:
        # Save the LoRA adapter
        print("\n💾 Saving model...")
        model.save_pretrained(OUTPUT_DIR)
        tokenizer.save_pretrained(OUTPUT_DIR)
        
        # Save training metadata
        metadata = {
            "model_name": MODEL_NAME,
            "training_time": str(training_time),
            "num_epochs": 1,
            "batch_size": 2,
            "learning_rate": 5e-4,
            "dataset_size": len(dataset),
            "max_length": MAX_LENGTH,
            "trainable_params": trainable_params,
            "total_params": total_params,
            "lora_config": {
                "r": lora_config.r,
                "lora_alpha": lora_config.lora_alpha,
                "lora_dropout": lora_config.lora_dropout,
            },
            "training_date": datetime.now().isoformat(),
            "training_success": True
        }
        
        with open(os.path.join(OUTPUT_DIR, "training_metadata.json"), "w") as f:
            json.dump(metadata, f, indent=2)
        
        print(f"✅ Model and adapters saved to: {OUTPUT_DIR}")
        print("🎉 Training completed successfully!")
        
        # Test the model
        print("\n🧪 Testing the fine-tuned model...")
        test_prompt = "Q: How do I create a Git repository?\nA:"
        inputs = tokenizer(test_prompt, return_tensors="pt")
        
        with torch.no_grad():
            outputs = model.generate(
                inputs.input_ids,
                max_new_tokens=50,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        print(f"Test input: {test_prompt}")
        print(f"Model output: {response}")
        
    else:
        print("❌ Training failed, but setup was successful")
        
        # Save metadata even if training failed
        metadata = {
            "model_name": MODEL_NAME,
            "training_time": str(training_time),
            "training_success": False,
            "error": "Training failed but environment is set up correctly"
        }
        
        with open(os.path.join(OUTPUT_DIR, "training_metadata.json"), "w") as f:
            json.dump(metadata, f, indent=2)

if __name__ == "__main__":
    main()
