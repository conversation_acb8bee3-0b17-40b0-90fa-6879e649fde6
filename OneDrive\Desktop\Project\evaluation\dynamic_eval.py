#!/usr/bin/env python3
"""
Dynamic evaluation script for testing agent runs and scoring plan quality.
Generates eval_dynamic.md report with 0-2 scoring table.
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import List, Dict
from datetime import datetime

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from test_prompts import get_test_prompts
from agent import CommandLineAgent

class DynamicEvaluator:
    def __init__(self, model_path: str = "training/adapters", 
                 base_model: str = "TinyLlama/TinyLlama-1.1B-Chat-v1.0"):
        """
        Initialize the dynamic evaluator.
        
        Args:
            model_path: Path to fine-tuned LoRA adapter
            base_model: Base model name
        """
        self.model_path = model_path
        self.base_model = base_model
        
    def run_agent_tests(self, use_base_model: bool = False) -> List[Dict]:
        """Run agent tests and collect results."""
        print(f"Running agent tests (base_model={use_base_model})...")
        
        # Initialize agent
        agent = CommandLineAgent(
            model_path=self.model_path,
            base_model=self.base_model,
            use_base_model=use_base_model
        )
        
        test_prompts = get_test_prompts()
        results = []
        
        for prompt_data in test_prompts:
            print(f"Testing: {prompt_data['prompt']}")
            
            try:
                # Process instruction
                result = agent.process_instruction(prompt_data['prompt'])
                
                # Add test metadata
                result.update({
                    "test_id": prompt_data['id'],
                    "category": prompt_data['category'],
                    "expected_commands": prompt_data.get('expected_commands', []),
                    "model_type": "base" if use_base_model else "fine_tuned"
                })
                
                results.append(result)
                
            except Exception as e:
                print(f"Error processing {prompt_data['id']}: {e}")
                error_result = {
                    "test_id": prompt_data['id'],
                    "instruction": prompt_data['prompt'],
                    "category": prompt_data['category'],
                    "plan": f"Error: {str(e)}",
                    "commands": [],
                    "commands_found": 0,
                    "expected_commands": prompt_data.get('expected_commands', []),
                    "model_type": "base" if use_base_model else "fine_tuned",
                    "error": True
                }
                results.append(error_result)
        
        return results
    
    def score_plan_quality(self, result: Dict) -> Dict:
        """
        Score plan quality on 0-2 scale.
        
        0: Poor - No relevant commands or completely incorrect
        1: Fair - Some relevant commands but incomplete or partially incorrect
        2: Good - Comprehensive and correct command sequence
        """
        instruction = result['instruction']
        plan = result['plan']
        commands = result['commands']
        expected_commands = result.get('expected_commands', [])
        
        score = 0
        explanation = ""
        
        # Check if there was an error
        if result.get('error', False):
            score = 0
            explanation = "Error occurred during processing"
        elif not commands:
            score = 0
            explanation = "No commands extracted from plan"
        else:
            # Evaluate based on command relevance and completeness
            relevant_commands = 0
            total_expected = len(expected_commands)
            
            for expected in expected_commands:
                for actual in commands:
                    # Check for keyword matches
                    expected_keywords = expected.lower().split()
                    actual_lower = actual.lower()
                    
                    if any(keyword in actual_lower for keyword in expected_keywords):
                        relevant_commands += 1
                        break
            
            # Scoring logic
            if relevant_commands == 0:
                score = 0
                explanation = "No relevant commands found"
            elif relevant_commands < total_expected or len(commands) < 2:
                score = 1
                explanation = f"Partially correct ({relevant_commands}/{total_expected} expected patterns found)"
            else:
                score = 2
                explanation = f"Comprehensive solution ({relevant_commands}/{total_expected} expected patterns found)"
        
        return {
            "quality_score": score,
            "explanation": explanation,
            "relevant_commands": relevant_commands if 'relevant_commands' in locals() else 0,
            "total_expected": len(expected_commands)
        }
    
    def generate_report(self, base_results: List[Dict], 
                       fine_tuned_results: List[Dict]) -> str:
        """Generate dynamic evaluation report."""
        
        # Score all results
        base_scored = []
        ft_scored = []
        
        for result in base_results:
            scoring = self.score_plan_quality(result)
            result.update(scoring)
            base_scored.append(result)
        
        for result in fine_tuned_results:
            scoring = self.score_plan_quality(result)
            result.update(scoring)
            ft_scored.append(result)
        
        # Generate report
        report = []
        report.append("# Dynamic Evaluation Report")
        report.append("")
        report.append("## Overview")
        report.append(f"- **Evaluation Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"- **Base Model**: {self.base_model}")
        report.append(f"- **Fine-tuned Model**: {self.model_path}")
        report.append(f"- **Test Cases**: {len(base_results)}")
        report.append("")
        
        # Summary statistics
        base_avg = sum(r['quality_score'] for r in base_scored) / len(base_scored)
        ft_avg = sum(r['quality_score'] for r in ft_scored) / len(ft_scored)
        
        report.append("## Summary Statistics")
        report.append(f"- **Base Model Average Score**: {base_avg:.2f}/2.0")
        report.append(f"- **Fine-tuned Model Average Score**: {ft_avg:.2f}/2.0")
        report.append(f"- **Improvement**: {ft_avg - base_avg:+.2f}")
        report.append("")
        
        # Scoring table
        report.append("## Plan Quality Scoring (0-2 Scale)")
        report.append("")
        report.append("| Test ID | Instruction | Base Score | FT Score | Category |")
        report.append("|---------|-------------|------------|----------|----------|")
        
        for base_r, ft_r in zip(base_scored, ft_scored):
            instruction_short = base_r['instruction'][:50] + "..." if len(base_r['instruction']) > 50 else base_r['instruction']
            report.append(f"| {base_r['test_id']} | {instruction_short} | {base_r['quality_score']}/2 | {ft_r['quality_score']}/2 | {base_r['category']} |")
        
        report.append("")
        
        # Detailed results
        report.append("## Detailed Results")
        report.append("")
        
        for i, (base_r, ft_r) in enumerate(zip(base_scored, ft_scored)):
            report.append(f"### Test {i+1}: {base_r['test_id']}")
            report.append(f"**Instruction**: {base_r['instruction']}")
            report.append(f"**Category**: {base_r['category']}")
            report.append("")
            
            report.append("#### Base Model")
            report.append(f"**Score**: {base_r['quality_score']}/2 - {base_r['explanation']}")
            report.append(f"**Plan**:")
            report.append("```")
            report.append(base_r['plan'])
            report.append("```")
            report.append(f"**Commands Found**: {base_r['commands_found']}")
            if base_r['commands']:
                report.append("**Extracted Commands**:")
                for cmd in base_r['commands']:
                    report.append(f"- `{cmd}`")
            report.append("")
            
            report.append("#### Fine-tuned Model")
            report.append(f"**Score**: {ft_r['quality_score']}/2 - {ft_r['explanation']}")
            report.append(f"**Plan**:")
            report.append("```")
            report.append(ft_r['plan'])
            report.append("```")
            report.append(f"**Commands Found**: {ft_r['commands_found']}")
            if ft_r['commands']:
                report.append("**Extracted Commands**:")
                for cmd in ft_r['commands']:
                    report.append(f"- `{cmd}`")
            report.append("")
            
            report.append("---")
            report.append("")
        
        # Score distribution
        report.append("## Score Distribution")
        report.append("")
        
        base_dist = {0: 0, 1: 0, 2: 0}
        ft_dist = {0: 0, 1: 0, 2: 0}
        
        for r in base_scored:
            base_dist[r['quality_score']] += 1
        for r in ft_scored:
            ft_dist[r['quality_score']] += 1
        
        report.append("| Score | Base Model | Fine-tuned Model |")
        report.append("|-------|------------|------------------|")
        for score in [0, 1, 2]:
            report.append(f"| {score}/2 | {base_dist[score]} | {ft_dist[score]} |")
        
        report.append("")
        
        return "\n".join(report)

def main():
    parser = argparse.ArgumentParser(description="Dynamic evaluation of agent performance")
    parser.add_argument("--model-path", default="training/adapters",
                       help="Path to fine-tuned LoRA adapter")
    parser.add_argument("--base-model", default="TinyLlama/TinyLlama-1.1B-Chat-v1.0",
                       help="Base model name")
    parser.add_argument("--output", default="eval_dynamic.md",
                       help="Output report file")
    parser.add_argument("--skip-base", action="store_true",
                       help="Skip base model evaluation (use cached results)")
    
    args = parser.parse_args()
    
    print("🎯 Dynamic Evaluation: Agent Performance Testing")
    print("=" * 60)
    
    evaluator = DynamicEvaluator(args.model_path, args.base_model)
    
    try:
        # Run base model tests
        if not args.skip_base:
            print("\n🤖 Testing base model...")
            base_results = evaluator.run_agent_tests(use_base_model=True)
            
            # Save base results for caching
            with open("base_results.json", "w") as f:
                json.dump(base_results, f, indent=2)
        else:
            print("\n📁 Loading cached base model results...")
            with open("base_results.json", "r") as f:
                base_results = json.load(f)
        
        # Run fine-tuned model tests
        print("\n🎯 Testing fine-tuned model...")
        fine_tuned_results = evaluator.run_agent_tests(use_base_model=False)
        
        # Generate report
        print("\n📄 Generating report...")
        report = evaluator.generate_report(base_results, fine_tuned_results)
        
        # Save report
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ Report saved to: {args.output}")
        
        # Print summary
        base_avg = sum(r.get('quality_score', 0) for r in base_results) / len(base_results)
        ft_avg = sum(r.get('quality_score', 0) for r in fine_tuned_results) / len(fine_tuned_results)
        
        print(f"📊 Base Model Average: {base_avg:.2f}/2.0")
        print(f"📊 Fine-tuned Model Average: {ft_avg:.2f}/2.0")
        print(f"📈 Improvement: {ft_avg - base_avg:+.2f}")
        
    except Exception as e:
        print(f"❌ Error during evaluation: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
