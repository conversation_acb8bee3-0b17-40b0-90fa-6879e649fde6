{"metadata": {"total_pairs": 59, "categories": ["docker", "venv", "git", "tar", "ssh", "grep", "network", "text", "bash", "gzip"], "created_date": "2025-06-17", "purpose": "Command-line assistant training data"}, "data": [{"question": "How do I create a new Git repository?", "answer": "To create a new Git repository, use: git init\nThis initializes a new Git repository in the current directory.", "command": "git init", "category": "git"}, {"question": "How do I clone a repository from GitHub?", "answer": "To clone a repository, use: git clone <repository-url>\nExample: git clone https://github.com/user/repo.git", "command": "git clone <repository-url>", "category": "git"}, {"question": "How do I check the status of my Git repository?", "answer": "Use: git status\nThis shows the current state of your working directory and staging area.", "command": "git status", "category": "git"}, {"question": "How do I add files to the staging area?", "answer": "To add files to staging area:\n- Add specific file: git add <filename>\n- Add all files: git add .\n- Add all modified files: git add -A", "command": "git add <filename>", "category": "git"}, {"question": "How do I commit changes with a message?", "answer": "Use: git commit -m \"Your commit message\"\nThis commits staged changes with a descriptive message.", "command": "git commit -m \"message\"", "category": "git"}, {"question": "How do I create a new branch?", "answer": "To create a new branch: git branch <branch-name>\nTo create and switch to it: git checkout -b <branch-name>", "command": "git checkout -b <branch-name>", "category": "git"}, {"question": "How do I switch to a different branch?", "answer": "Use: git checkout <branch-name>\nOr with newer Git versions: git switch <branch-name>", "command": "git checkout <branch-name>", "category": "git"}, {"question": "How do I merge a branch into main?", "answer": "First switch to main: git checkout main\nThen merge: git merge <branch-name>", "command": "git merge <branch-name>", "category": "git"}, {"question": "How do I push changes to remote repository?", "answer": "Use: git push origin <branch-name>\nFor first push: git push -u origin <branch-name>", "command": "git push origin <branch-name>", "category": "git"}, {"question": "How do I pull latest changes from remote?", "answer": "Use: git pull origin <branch-name>\nOr simply: git pull (if tracking branch is set)", "command": "git pull", "category": "git"}, {"question": "How do I view commit history?", "answer": "Use: git log\nFor compact view: git log --oneline\nFor graph view: git log --graph --oneline", "command": "git log", "category": "git"}, {"question": "How do I undo the last commit but keep changes?", "answer": "Use: git reset --soft HEAD~1\nThis undoes the commit but keeps changes staged.", "command": "git reset --soft HEAD~1", "category": "git"}, {"question": "How do I see differences between working directory and last commit?", "answer": "Use: git diff\nFor staged changes: git diff --cached", "command": "git diff", "category": "git"}, {"question": "How do I delete a branch?", "answer": "Delete local branch: git branch -d <branch-name>\nForce delete: git branch -D <branch-name>\nDelete remote branch: git push origin --delete <branch-name>", "command": "git branch -d <branch-name>", "category": "git"}, {"question": "How do I stash my current changes?", "answer": "Use: git stash\nTo stash with message: git stash save \"message\"\nTo apply stash: git stash pop", "command": "git stash", "category": "git"}, {"question": "How do I list all files in a directory?", "answer": "Use: ls\nFor detailed listing: ls -l\nInclude hidden files: ls -la", "command": "ls -la", "category": "bash"}, {"question": "How do I change to a different directory?", "answer": "Use: cd <directory-path>\nTo go to home directory: cd ~\nTo go up one level: cd ..", "command": "cd <directory-path>", "category": "bash"}, {"question": "How do I create a new directory?", "answer": "Use: mkdir <directory-name>\nTo create nested directories: mkdir -p path/to/directory", "command": "mkdir <directory-name>", "category": "bash"}, {"question": "How do I copy files?", "answer": "Copy file: cp <source> <destination>\nCopy directory recursively: cp -r <source-dir> <dest-dir>", "command": "cp <source> <destination>", "category": "bash"}, {"question": "How do I move or rename files?", "answer": "Use: mv <source> <destination>\nThis works for both moving and renaming files/directories.", "command": "mv <source> <destination>", "category": "bash"}, {"question": "How do I delete files?", "answer": "Delete file: rm <filename>\nDelete directory: rm -r <directory>\nForce delete: rm -rf <path>", "command": "rm <filename>", "category": "bash"}, {"question": "How do I view the contents of a file?", "answer": "Use: cat <filename>\nFor large files: less <filename> or more <filename>\nFirst few lines: head <filename>", "command": "cat <filename>", "category": "bash"}, {"question": "How do I find my current directory?", "answer": "Use: pwd\nThis prints the current working directory path.", "command": "pwd", "category": "bash"}, {"question": "How do I check disk usage?", "answer": "Use: df -h (filesystem usage)\nFor directory size: du -sh <directory>\nFor current directory: du -sh .", "command": "df -h", "category": "bash"}, {"question": "How do I check running processes?", "answer": "Use: ps aux (all processes)\nFor interactive view: top or htop\nFind specific process: ps aux | grep <process-name>", "command": "ps aux", "category": "bash"}, {"question": "How do I kill a process?", "answer": "Use: kill <process-id>\nForce kill: kill -9 <process-id>\nKill by name: killall <process-name>", "command": "kill <process-id>", "category": "bash"}, {"question": "How do I check file permissions?", "answer": "Use: ls -l <filename>\nThe first column shows permissions (rwxrwxrwx format).", "command": "ls -l <filename>", "category": "bash"}, {"question": "How do I change file permissions?", "answer": "Use: chmod <permissions> <filename>\nExample: chmod 755 script.sh\nOr: chmod +x script.sh (make executable)", "command": "chmod 755 <filename>", "category": "bash"}, {"question": "How do I create an empty file?", "answer": "Use: touch <filename>\nThis creates an empty file or updates timestamp if file exists.", "command": "touch <filename>", "category": "bash"}, {"question": "How do I find files by name?", "answer": "Use: find <directory> -name \"<pattern>\"\nExample: find . -name \"*.py\"\nCase insensitive: find . -iname \"*.txt\"", "command": "find . -name \"<pattern>\"", "category": "bash"}, {"question": "How do I create a tar archive?", "answer": "Use: tar -cvf archive.tar files/\nWith compression: tar -czvf archive.tar.gz files/", "command": "tar -czvf archive.tar.gz files/", "category": "tar"}, {"question": "How do I extract a tar archive?", "answer": "Use: tar -xvf archive.tar\nFor compressed: tar -xzvf archive.tar.gz", "command": "tar -xzvf archive.tar.gz", "category": "tar"}, {"question": "How do I list contents of a tar file?", "answer": "Use: tar -tvf archive.tar\nFor compressed: tar -tzvf archive.tar.gz", "command": "tar -tzvf archive.tar.gz", "category": "tar"}, {"question": "How do I compress a file with gzip?", "answer": "Use: gzip filename\nThis creates filename.gz and removes original.\nKeep original: gzip -k filename", "command": "gzip filename", "category": "gzip"}, {"question": "How do I decompress a gzip file?", "answer": "Use: gunzip filename.gz\nOr: gzip -d filename.gz", "command": "gunzip filename.gz", "category": "gzip"}, {"question": "How do I search for text in a file?", "answer": "Use: grep \"pattern\" filename\nCase insensitive: grep -i \"pattern\" filename", "command": "grep \"pattern\" filename", "category": "grep"}, {"question": "How do I search recursively in directories?", "answer": "Use: grep -r \"pattern\" directory/\nInclude line numbers: grep -rn \"pattern\" directory/", "command": "grep -rn \"pattern\" directory/", "category": "grep"}, {"question": "How do I search for whole words only?", "answer": "Use: grep -w \"word\" filename\nThis matches complete words, not partial matches.", "command": "grep -w \"word\" filename", "category": "grep"}, {"question": "How do I count matching lines?", "answer": "Use: grep -c \"pattern\" filename\nThis returns the count of matching lines.", "command": "grep -c \"pattern\" filename", "category": "grep"}, {"question": "How do I show lines before and after matches?", "answer": "Use: grep -A 3 -B 3 \"pattern\" filename\n-A: lines after, -B: lines before, -C: lines around", "command": "grep -A 3 -B 3 \"pattern\" filename", "category": "grep"}, {"question": "How do I create a Python virtual environment?", "answer": "Use: python -m venv myenv\nOr: python3 -m venv myenv\nThis creates a new virtual environment named 'myenv'.", "command": "python -m venv myenv", "category": "venv"}, {"question": "How do I activate a virtual environment?", "answer": "On Linux/Mac: source myenv/bin/activate\nOn Windows: myenv\\Scripts\\activate", "command": "source myenv/bin/activate", "category": "venv"}, {"question": "How do I deactivate a virtual environment?", "answer": "Simply use: deactivate\nThis works regardless of the operating system.", "command": "deactivate", "category": "venv"}, {"question": "How do I install packages in a virtual environment?", "answer": "First activate the environment, then: pip install package-name\nInstall from requirements: pip install -r requirements.txt", "command": "pip install package-name", "category": "venv"}, {"question": "How do I list installed packages in virtual environment?", "answer": "Use: pip list\nOr for requirements format: pip freeze\nSave to file: pip freeze > requirements.txt", "command": "pip freeze", "category": "venv"}, {"question": "How do I connect to a remote server via SSH?", "answer": "Use: ssh username@hostname\nWith specific port: ssh -p 2222 username@hostname", "command": "ssh username@hostname", "category": "ssh"}, {"question": "How do I copy files over SSH?", "answer": "Use: scp file.txt username@hostname:/path/\nCopy directory: scp -r directory/ username@hostname:/path/", "command": "scp file.txt username@hostname:/path/", "category": "ssh"}, {"question": "How do I generate SSH keys?", "answer": "Use: ssh-keygen -t rsa -b 4096\nFor Ed25519: ssh-keygen -t ed25519", "command": "ssh-keygen -t rsa -b 4096", "category": "ssh"}, {"question": "How do I list Docker containers?", "answer": "Use: docker ps (running containers)\nAll containers: docker ps -a", "command": "docker ps -a", "category": "docker"}, {"question": "How do I run a Docker container?", "answer": "Use: docker run image-name\nInteractive mode: docker run -it image-name\nDetached mode: docker run -d image-name", "command": "docker run -it image-name", "category": "docker"}, {"question": "How do I stop a Docker container?", "answer": "Use: docker stop container-id\nOr: docker stop container-name", "command": "docker stop container-id", "category": "docker"}, {"question": "How do I build a <PERSON><PERSON> image?", "answer": "Use: docker build -t image-name .\nWith specific Dockerfile: docker build -f Dockerfile.dev -t image-name .", "command": "docker build -t image-name .", "category": "docker"}, {"question": "How do I check network connectivity?", "answer": "Use: ping hostname\nExample: ping google.com\nLimit packets: ping -c 4 hostname", "command": "ping google.com", "category": "network"}, {"question": "How do I check open ports?", "answer": "Use: netstat -tuln\nOr: ss -tuln\nFor specific port: netstat -tuln | grep :80", "command": "netstat -tuln", "category": "network"}, {"question": "How do I download a file from URL?", "answer": "Use: wget URL\nOr: curl -O URL\nWith custom name: curl -o filename URL", "command": "wget URL", "category": "network"}, {"question": "How do I count lines in a file?", "answer": "Use: wc -l filename\nCount words: wc -w filename\nCount characters: wc -c filename", "command": "wc -l filename", "category": "text"}, {"question": "How do I sort lines in a file?", "answer": "Use: sort filename\nReverse order: sort -r filename\nNumeric sort: sort -n filename", "command": "sort filename", "category": "text"}, {"question": "How do I remove duplicate lines?", "answer": "Use: sort filename | uniq\nOr: sort -u filename\nCount duplicates: sort filename | uniq -c", "command": "sort filename | uniq", "category": "text"}, {"question": "How do I replace text in a file?", "answer": "Use: sed 's/old/new/g' filename\nIn-place edit: sed -i 's/old/new/g' filename", "command": "sed 's/old/new/g' filename", "category": "text"}]}