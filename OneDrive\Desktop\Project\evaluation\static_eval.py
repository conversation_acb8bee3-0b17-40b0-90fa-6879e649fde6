#!/usr/bin/env python3
"""
Static evaluation script for comparing base vs fine-tuned model outputs.
Computes BLEU and ROUGE-L metrics and generates eval_static.md report.
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import List, Dict, Tuple

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from test_prompts import get_test_prompts
from agent import CommandLineAgent

try:
    from rouge_score import rouge_scorer
    from sacrebleu import BLEU
except ImportError:
    print("Installing required packages...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "rouge-score", "sacrebleu"])
    from rouge_score import rouge_scorer
    from sacrebleu import BLEU

class StaticEvaluator:
    def __init__(self, base_model: str = "TinyLlama/TinyLlama-1.1B-Chat-v1.0", 
                 fine_tuned_path: str = "training/adapters"):
        """
        Initialize the static evaluator.
        
        Args:
            base_model: Base model name
            fine_tuned_path: Path to fine-tuned LoRA adapter
        """
        self.base_model = base_model
        self.fine_tuned_path = fine_tuned_path
        self.rouge_scorer = rouge_scorer.RougeScorer(['rougeL'], use_stemmer=True)
        self.bleu = BLEU()
        
    def load_agents(self) -> Tuple[CommandLineAgent, CommandLineAgent]:
        """Load base and fine-tuned agents."""
        print("Loading base model agent...")
        base_agent = CommandLineAgent(
            base_model=self.base_model,
            use_base_model=True
        )
        
        print("Loading fine-tuned model agent...")
        fine_tuned_agent = CommandLineAgent(
            model_path=self.fine_tuned_path,
            base_model=self.base_model,
            use_base_model=False
        )
        
        return base_agent, fine_tuned_agent
    
    def generate_responses(self, agent: CommandLineAgent, prompts: List[Dict]) -> List[Dict]:
        """Generate responses for all test prompts."""
        responses = []
        
        for prompt_data in prompts:
            print(f"Generating response for: {prompt_data['prompt']}")
            
            try:
                response = agent.generate_plan(prompt_data['prompt'])
                commands = agent.parse_commands(response)
                
                result = {
                    "prompt_id": prompt_data['id'],
                    "prompt": prompt_data['prompt'],
                    "response": response,
                    "commands": commands,
                    "category": prompt_data['category']
                }
                responses.append(result)
                
            except Exception as e:
                print(f"Error generating response: {e}")
                result = {
                    "prompt_id": prompt_data['id'],
                    "prompt": prompt_data['prompt'],
                    "response": f"Error: {str(e)}",
                    "commands": [],
                    "category": prompt_data['category']
                }
                responses.append(result)
        
        return responses
    
    def compute_metrics(self, base_responses: List[Dict], 
                       fine_tuned_responses: List[Dict]) -> Dict:
        """Compute BLEU and ROUGE-L metrics."""
        metrics = {
            "bleu_scores": [],
            "rouge_scores": [],
            "average_bleu": 0.0,
            "average_rouge": 0.0
        }
        
        for base_resp, ft_resp in zip(base_responses, fine_tuned_responses):
            # BLEU score (fine-tuned vs base as reference)
            try:
                bleu_score = self.bleu.sentence_score(
                    ft_resp['response'], 
                    [base_resp['response']]
                ).score
            except:
                bleu_score = 0.0
            
            # ROUGE-L score
            rouge_scores = self.rouge_scorer.score(
                base_resp['response'], 
                ft_resp['response']
            )
            rouge_l_score = rouge_scores['rougeL'].fmeasure
            
            metrics["bleu_scores"].append(bleu_score)
            metrics["rouge_scores"].append(rouge_l_score)
        
        # Calculate averages
        if metrics["bleu_scores"]:
            metrics["average_bleu"] = sum(metrics["bleu_scores"]) / len(metrics["bleu_scores"])
        if metrics["rouge_scores"]:
            metrics["average_rouge"] = sum(metrics["rouge_scores"]) / len(metrics["rouge_scores"])
        
        return metrics
    
    def evaluate_command_quality(self, responses: List[Dict], 
                                test_prompts: List[Dict]) -> List[Dict]:
        """Evaluate command quality on a 0-2 scale."""
        evaluations = []
        
        for resp, prompt_data in zip(responses, test_prompts):
            expected_commands = prompt_data.get('expected_commands', [])
            actual_commands = resp['commands']
            
            # Simple scoring based on command presence
            score = 0
            if actual_commands:
                # Check if any expected command patterns are found
                for expected in expected_commands:
                    for actual in actual_commands:
                        if any(keyword in actual.lower() for keyword in expected.lower().split()):
                            score = 1
                            break
                    if score > 0:
                        break
                
                # Bonus point for multiple relevant commands
                if len(actual_commands) > 1 and score > 0:
                    score = 2
            
            evaluation = {
                "prompt_id": resp['prompt_id'],
                "prompt": resp['prompt'],
                "commands": actual_commands,
                "expected_commands": expected_commands,
                "quality_score": score,
                "explanation": self._get_score_explanation(score)
            }
            evaluations.append(evaluation)
        
        return evaluations
    
    def _get_score_explanation(self, score: int) -> str:
        """Get explanation for quality score."""
        explanations = {
            0: "No relevant commands found or incorrect approach",
            1: "Basic relevant commands found, partially correct",
            2: "Comprehensive and correct command sequence"
        }
        return explanations.get(score, "Unknown score")
    
    def generate_report(self, base_responses: List[Dict], 
                       fine_tuned_responses: List[Dict],
                       metrics: Dict, test_prompts: List[Dict]) -> str:
        """Generate markdown report."""
        
        # Evaluate command quality
        base_quality = self.evaluate_command_quality(base_responses, test_prompts)
        ft_quality = self.evaluate_command_quality(fine_tuned_responses, test_prompts)
        
        report = []
        report.append("# Static Evaluation Report")
        report.append("")
        report.append("## Overview")
        report.append(f"- **Base Model**: {self.base_model}")
        report.append(f"- **Fine-tuned Model**: {self.fine_tuned_path}")
        report.append(f"- **Test Prompts**: {len(test_prompts)}")
        report.append("")
        
        report.append("## Metrics Summary")
        report.append(f"- **Average BLEU Score**: {metrics['average_bleu']:.4f}")
        report.append(f"- **Average ROUGE-L Score**: {metrics['average_rouge']:.4f}")
        report.append("")
        
        # Quality scores
        base_avg_quality = sum(q['quality_score'] for q in base_quality) / len(base_quality)
        ft_avg_quality = sum(q['quality_score'] for q in ft_quality) / len(ft_quality)
        
        report.append("## Quality Scores (0-2 scale)")
        report.append(f"- **Base Model Average**: {base_avg_quality:.2f}")
        report.append(f"- **Fine-tuned Model Average**: {ft_avg_quality:.2f}")
        report.append("")
        
        report.append("## Detailed Comparison")
        report.append("")
        
        for i, (base_resp, ft_resp, prompt_data) in enumerate(zip(base_responses, fine_tuned_responses, test_prompts)):
            report.append(f"### Test {i+1}: {prompt_data['prompt']}")
            report.append(f"**Category**: {prompt_data['category']}")
            report.append("")
            
            report.append("**Base Model Response:**")
            report.append(f"```")
            report.append(base_resp['response'])
            report.append(f"```")
            report.append("")
            
            report.append("**Fine-tuned Model Response:**")
            report.append(f"```")
            report.append(ft_resp['response'])
            report.append(f"```")
            report.append("")
            
            report.append("**Metrics:**")
            report.append(f"- BLEU: {metrics['bleu_scores'][i]:.4f}")
            report.append(f"- ROUGE-L: {metrics['rouge_scores'][i]:.4f}")
            report.append(f"- Base Quality Score: {base_quality[i]['quality_score']}/2")
            report.append(f"- Fine-tuned Quality Score: {ft_quality[i]['quality_score']}/2")
            report.append("")
            report.append("---")
            report.append("")
        
        return "\n".join(report)

def main():
    parser = argparse.ArgumentParser(description="Static evaluation of base vs fine-tuned models")
    parser.add_argument("--base-model", default="TinyLlama/TinyLlama-1.1B-Chat-v1.0",
                       help="Base model name")
    parser.add_argument("--fine-tuned-path", default="training/adapters",
                       help="Path to fine-tuned LoRA adapter")
    parser.add_argument("--output", default="eval_static.md",
                       help="Output report file")
    
    args = parser.parse_args()
    
    print("🔬 Static Evaluation: Base vs Fine-tuned Models")
    print("=" * 60)
    
    # Initialize evaluator
    evaluator = StaticEvaluator(args.base_model, args.fine_tuned_path)
    
    # Get test prompts
    test_prompts = get_test_prompts()
    print(f"📝 Testing with {len(test_prompts)} prompts")
    
    try:
        # Load agents
        base_agent, fine_tuned_agent = evaluator.load_agents()
        
        # Generate responses
        print("\n🤖 Generating base model responses...")
        base_responses = evaluator.generate_responses(base_agent, test_prompts)
        
        print("\n🎯 Generating fine-tuned model responses...")
        fine_tuned_responses = evaluator.generate_responses(fine_tuned_agent, test_prompts)
        
        # Compute metrics
        print("\n📊 Computing metrics...")
        metrics = evaluator.compute_metrics(base_responses, fine_tuned_responses)
        
        # Generate report
        print("\n📄 Generating report...")
        report = evaluator.generate_report(base_responses, fine_tuned_responses, metrics, test_prompts)
        
        # Save report
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ Report saved to: {args.output}")
        print(f"📊 Average BLEU: {metrics['average_bleu']:.4f}")
        print(f"📊 Average ROUGE-L: {metrics['average_rouge']:.4f}")
        
    except Exception as e:
        print(f"❌ Error during evaluation: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
