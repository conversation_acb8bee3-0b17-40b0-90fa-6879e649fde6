  0%|                                                                                            | 0/13 [00:00<?, ?it/s]`loss_type=None` was set in the config but it is unrecognised.Using the default loss: `ForCausalLMLoss`.
100%|███████████████████████████████████████████████████████████████████████████████████| 13/13 [01:07<00:00,  5.21s/it]
{'loss': 14.7803, 'grad_norm': 1.9926252365112305, 'learning_rate': 0.0002, 'epoch': 0.4}
{'loss': 14.2558, 'grad_norm': 2.330742120742798, 'learning_rate': 0.00045000000000000004, 'epoch': 0.8}
{'train_runtime': 69.0188, 'train_samples_per_second': 0.724, 'train_steps_per_second': 0.188, 'train_loss': 14.535589364858774, 'epoch': 1.0}

⏱️ Training completed in: 0:01:09.231190

💾 Saving model...
✅ Model and adapters saved to: training/adapters
🎉 Training completed successfully!

🧪 Testing the fine-tuned model...
The attention mask is not set and cannot be inferred from input because pad token is same as eos token. As a consequence, you may observe unexpected behavior. Please pass your input's `attention_mask` to obtain reliable results.
Test input: Q: How do I create a Git repository?
A:
Model output: Q: How do I create a Git repository?
A: quick question, how do you access git repositories intercourse? Quicknix?
