{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Command-Line Assistant Fine-Tuning\n", "\n", "This notebook fine-tunes a small language model (TinyLlama-1.1B or Phi-2) using LoRA/QLoRA on command-line Q&A data.\n", "\n", "**Requirements:**\n", "- Google Colab with T4 GPU (free tier)\n", "- ~2-3 hours training time\n", "- Model ≤2B parameters\n", "- 1 epoch training"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup Environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install -q transformers datasets peft accelerate bitsandbytes wandb\n", "!pip install -q torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import torch\n", "from datetime import datetime\n", "from pathlib import Path\n", "\n", "from transformers import (\n", "    AutoTokenizer, \n", "    AutoModelForCausalLM,\n", "    TrainingArguments,\n", "    Trainer,\n", "    DataCollatorForLanguageModeling,\n", "    BitsAndBytesConfig\n", ")\n", "from datasets import Dataset\n", "from peft import LoraConfig, get_peft_model, TaskType\n", "import wandb\n", "\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "if torch.cuda.is_available():\n", "    print(f\"GPU: {torch.cuda.get_device_name(0)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Upload and Load Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Upload your command_line_qa.json file to Colab\n", "from google.colab import files\n", "\n", "print(\"Please upload your command_line_qa.json file:\")\n", "uploaded = files.upload()\n", "\n", "# Verify upload\n", "data_path = \"command_line_qa.json\"\n", "if os.path.exists(data_path):\n", "    print(f\"✓ Dataset uploaded successfully: {data_path}\")\n", "else:\n", "    print(\"❌ Dataset not found. Please upload the file.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_dataset(data_path: str):\n", "    \"\"\"Load and preprocess the command-line Q&A dataset.\"\"\"\n", "    print(f\"Loading dataset from {data_path}\")\n", "    \n", "    with open(data_path, 'r', encoding='utf-8') as f:\n", "        data = json.load(f)\n", "    \n", "    qa_pairs = data['data']\n", "    print(f\"Loaded {len(qa_pairs)} Q&A pairs\")\n", "    print(f\"Categories: {data['metadata']['categories']}\")\n", "    \n", "    # Format data for instruction tuning\n", "    formatted_data = []\n", "    for pair in qa_pairs:\n", "        # Create instruction-following format\n", "        instruction = f\"Question: {pair['question']}\\nAnswer:\"\n", "        response = f\" {pair['answer']}\"\n", "        \n", "        # Combine for causal language modeling\n", "        text = instruction + response\n", "        formatted_data.append({\"text\": text})\n", "    \n", "    return Dataset.from_list(formatted_data)\n", "\n", "# Load dataset\n", "dataset = load_dataset(data_path)\n", "print(f\"\\nSample entry:\")\n", "print(dataset[0]['text'][:200] + \"...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Model Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Model configuration\n", "MODEL_NAME = \"TinyLlama/TinyLlama-1.1B-Chat-v1.0\"  # or \"microsoft/phi-2\"\n", "MAX_LENGTH = 512\n", "USE_4BIT = True\n", "\n", "print(f\"Selected model: {MODEL_NAME}\")\n", "print(f\"Max sequence length: {MAX_LENGTH}\")\n", "print(f\"4-bit quantization: {USE_4BIT}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def setup_model_and_tokenizer(model_name: str, use_4bit: bool = True):\n", "    \"\"\"Setup model and tokenizer with optional 4-bit quantization.\"\"\"\n", "    print(f\"Loading model: {model_name}\")\n", "    \n", "    # Load tokenizer\n", "    tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "    if tokenizer.pad_token is None:\n", "        tokenizer.pad_token = tokenizer.eos_token\n", "    \n", "    # Load model with optional quantization\n", "    if use_4bit:\n", "        bnb_config = BitsAndBytesConfig(\n", "            load_in_4bit=True,\n", "            bnb_4bit_quant_type=\"nf4\",\n", "            bnb_4bit_compute_dtype=torch.float16,\n", "            bnb_4bit_use_double_quant=True,\n", "        )\n", "        model = AutoModelForCausalLM.from_pretrained(\n", "            model_name,\n", "            quantization_config=bnb_config,\n", "            device_map=\"auto\",\n", "            trust_remote_code=True\n", "        )\n", "    else:\n", "        model = AutoModelForCausalLM.from_pretrained(\n", "            model_name,\n", "            torch_dtype=torch.float16,\n", "            device_map=\"auto\",\n", "            trust_remote_code=True\n", "        )\n", "    \n", "    return model, tokenizer\n", "\n", "# Setup model and tokenizer\n", "model, tokenizer = setup_model_and_tokenizer(MODEL_NAME, USE_4BIT)\n", "print(f\"✓ Model and tokenizer loaded successfully\")\n", "print(f\"Model parameters: {model.num_parameters():,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Data Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def tokenize_function(examples, tokenizer, max_length=512):\n", "    \"\"\"Tokenize the dataset.\"\"\"\n", "    return tokenizer(\n", "        examples[\"text\"],\n", "        truncation=True,\n", "        padding=False,\n", "        max_length=max_length,\n", "        return_overflowing_tokens=False,\n", "    )\n", "\n", "# Tokenize dataset\n", "print(\"Tokenizing dataset...\")\n", "tokenized_dataset = dataset.map(\n", "    lambda x: tokenize_function(x, tokenizer, MAX_LENGTH),\n", "    batched=True,\n", "    remove_columns=dataset.column_names\n", ")\n", "\n", "print(f\"✓ Dataset tokenized: {len(tokenized_dataset)} examples\")\n", "print(f\"Sample tokenized length: {len(tokenized_dataset[0]['input_ids'])}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. LoRA Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def setup_lora_config():\n", "    \"\"\"Setup LoRA configuration.\"\"\"\n", "    lora_config = LoraConfig(\n", "        task_type=TaskType.CAUSAL_LM,\n", "        inference_mode=False,\n", "        r=16,  # LoRA rank\n", "        lora_alpha=32,  # LoRA scaling parameter\n", "        lora_dropout=0.1,\n", "        target_modules=[\"q_proj\", \"v_proj\", \"k_proj\", \"o_proj\", \"gate_proj\", \"up_proj\", \"down_proj\"]\n", "    )\n", "    return lora_config\n", "\n", "# Setup LoRA\n", "lora_config = setup_lora_config()\n", "model = get_peft_model(model, lora_config)\n", "\n", "trainable_params = model.num_parameters()\n", "total_params = model.num_parameters(only_trainable=False)\n", "\n", "print(f\"✓ LoRA applied successfully\")\n", "print(f\"Trainable parameters: {trainable_params:,}\")\n", "print(f\"Total parameters: {total_params:,}\")\n", "print(f\"Trainable %: {100 * trainable_params / total_params:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Training Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Training hyperparameters\n", "OUTPUT_DIR = \"./command_line_assistant_lora\"\n", "BATCH_SIZE = 4\n", "LEARNING_RATE = 2e-4\n", "NUM_EPOCHS = 1\n", "\n", "# Create output directory\n", "os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "\n", "# Training arguments\n", "training_args = TrainingArguments(\n", "    output_dir=OUTPUT_DIR,\n", "    num_train_epochs=NUM_EPOCHS,\n", "    per_device_train_batch_size=BATCH_SIZE,\n", "    gradient_accumulation_steps=4,\n", "    warmup_steps=100,\n", "    learning_rate=LEARNING_RATE,\n", "    fp16=True,\n", "    logging_steps=10,\n", "    save_strategy=\"epoch\",\n", "    evaluation_strategy=\"no\",\n", "    save_total_limit=1,\n", "    remove_unused_columns=False,\n", "    dataloader_pin_memory=False,\n", ")\n", "\n", "# Data collator\n", "data_collator = DataCollatorForLanguageModeling(\n", "    tokenizer=tokenizer,\n", "    mlm=False,\n", ")\n", "\n", "print(f\"✓ Training configuration set\")\n", "print(f\"Output directory: {OUTPUT_DIR}\")\n", "print(f\"Batch size: {BATCH_SIZE}\")\n", "print(f\"Learning rate: {LEARNING_RATE}\")\n", "print(f\"Epochs: {NUM_EPOCHS}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create trainer\n", "trainer = Trainer(\n", "    model=model,\n", "    args=training_args,\n", "    train_dataset=tokenized_dataset,\n", "    data_collator=data_collator,\n", ")\n", "\n", "print(\"✓ Trainer created successfully\")\n", "print(f\"Training dataset size: {len(tokenized_dataset)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Start training\n", "print(\"🚀 Starting training...\")\n", "start_time = datetime.now()\n", "\n", "trainer.train()\n", "\n", "end_time = datetime.now()\n", "training_time = end_time - start_time\n", "print(f\"\\n✅ Training completed in: {training_time}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Save Model and Metada<PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save the LoRA adapter\n", "model.save_pretrained(OUTPUT_DIR)\n", "tokenizer.save_pretrained(OUTPUT_DIR)\n", "\n", "# Save training metadata\n", "metadata = {\n", "    \"model_name\": MODEL_NAME,\n", "    \"training_time\": str(training_time),\n", "    \"num_epochs\": NUM_EPOCHS,\n", "    \"batch_size\": BATCH_SIZE,\n", "    \"learning_rate\": LEARNING_RATE,\n", "    \"dataset_size\": len(dataset),\n", "    \"max_length\": MAX_LENGTH,\n", "    \"trainable_params\": trainable_params,\n", "    \"total_params\": total_params,\n", "    \"lora_config\": {\n", "        \"r\": lora_config.r,\n", "        \"lora_alpha\": lora_config.lora_alpha,\n", "        \"lora_dropout\": lora_config.lora_dropout,\n", "    },\n", "    \"training_date\": datetime.now().isoformat()\n", "}\n", "\n", "with open(os.path.join(OUTPUT_DIR, \"training_metadata.json\"), \"w\") as f:\n", "    json.dump(metadata, f, indent=2)\n", "\n", "print(f\"✅ Model and adapters saved to: {OUTPUT_DIR}\")\n", "print(f\"📊 Training metadata saved\")\n", "print(\"\\n🎉 Training completed successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Download Trained Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a zip file of the trained model\n", "import shutil\n", "\n", "archive_name = \"command_line_assistant_lora\"\n", "shutil.make_archive(archive_name, 'zip', OUTPUT_DIR)\n", "\n", "print(f\"📦 Model archived as: {archive_name}.zip\")\n", "print(\"📥 Download the zip file to use in your local environment\")\n", "\n", "# Download the model\n", "files.download(f\"{archive_name}.zip\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}