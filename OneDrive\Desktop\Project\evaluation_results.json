{"evaluation_date": "2025-06-18T00:56:09.681542", "test_results": [{"test_id": 1, "prompt": "Create a new Git branch and switch to it", "base_model": {"plan": "Git command prompt", "commands": [], "score": 1, "success": true}, "fine_tuned_model": {"plan": "git branch", "commands": [], "score": 1, "success": true}}, {"test_id": 2, "prompt": "List all Python files in the current directory", "base_model": {"plan": "rsync", "commands": [], "score": 0, "success": true}, "fine_tuned_model": {"plan": "ls command sed sed sed command sed sed sed sed command sed sed sed sed", "commands": [], "score": 1, "success": true}}, {"test_id": 3, "prompt": "Create a virtual environment and activate it", "base_model": {"plan": "Computer shutdown Windows", "commands": [], "score": 0, "success": true}, "fine_tuned_model": {"plan": "create virtual environment Command : create virtual environment", "commands": [], "score": 0, "success": true}}, {"test_id": 4, "prompt": "Compress a folder using tar with gzip", "base_model": {"plan": "run tar.gz", "commands": [], "score": 1, "success": true}, "fine_tuned_model": {"plan": "Makefile : Use the tar's path Makefile", "commands": [], "score": 1, "success": true}}, {"test_id": 5, "prompt": "Search for text 'TODO' in all files recursively", "base_model": {"plan": "gettext'Todo '", "commands": [], "score": 0, "success": true}, "fine_tuned_model": {"plan": "help text 2", "commands": [], "score": 0, "success": true}}, {"test_id": 6, "prompt": "Set up a complete development environment with Git, virtual environment, and install dependencies", "base_model": {"plan": "git checkout 1.7. 7", "commands": [], "score": 1, "success": true}, "fine_tuned_model": {"plan": "lisp git", "commands": [], "score": 1, "success": true}}, {"test_id": 7, "prompt": "Find and delete all log files older than 7 days", "base_model": {"plan": "logma logmas logmas logmas Logmas", "commands": [], "score": 0, "success": true}, "fine_tuned_model": {"plan": "log.txt", "commands": [], "score": 0, "success": true}}], "summary": {"base_model_average": 0.42857142857142855, "fine_tuned_average": 0.5714285714285714, "improvement": 0.14285714285714285}}