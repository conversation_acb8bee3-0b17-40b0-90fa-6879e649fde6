# Demo Video Script
**Command-Line Assistant with Fine-Tuned Language Model**

## Video Outline (≤5 minutes)

### Introduction (30 seconds)
- "Welcome to the Command-Line Assistant demo"
- "This project implements an AI-powered assistant for command-line tasks"
- "Built for Fenrir Security's AI/ML internship technical task"

### Project Overview (45 seconds)
- Show project structure in file explorer
- Highlight key components:
  - `data/` - 153 Q&A pairs covering 22 command-line categories
  - `training/` - LoRA fine-tuning setup for TinyLlama-1.1B
  - `agent.py` - Main CLI agent script
  - `evaluation/` - Static and dynamic evaluation scripts

### Dataset Demonstration (30 seconds)
- Open `data/command_line_qa.json`
- Show sample Q&A pairs
- Highlight categories: Git, Bash, tar/gzip, grep, venv, SSH, Docker, etc.
- "153 validated Q&A pairs exceeding the required 150"

### Training Setup (45 seconds)
- Show `training/train.py` and `train.ipynb`
- Explain LoRA/QLoRA configuration
- Highlight key parameters:
  - TinyLlama-1.1B base model (≤2B requirement)
  - LoRA rank 16, alpha 32
  - 1 epoch training as required
  - 4-bit quantization for efficiency

### CLI Agent Demo (90 seconds)
**Terminal Demo:**

```bash
# Demo 1: Git operations
python demo_agent.py "Create a new Git branch and switch to it"
```
- Show generated plan with step-by-step instructions
- Highlight extracted commands: `git checkout -b <branch-name>`
- Show dry-run execution and logging

```bash
# Demo 2: File operations
python demo_agent.py "List all Python files in current directory"
```
- Show multiple command options provided
- Demonstrate comprehensive response vs simple command

```bash
# Demo 3: Complex workflow
python demo_agent.py "Set up a complete development environment with Git, virtual environment, and install dependencies"
```
- Show multi-step plan generation
- Highlight command sequence extraction

### Evaluation Results (45 seconds)
- Open `eval_static.md`
- Show BLEU/ROUGE-L metrics comparison
- Highlight quality score improvements: Base 1.14/2.0 → Fine-tuned 1.71/2.0

- Open `eval_dynamic.md`
- Show plan quality scoring table
- Demonstrate improvement across all test categories

### Logging and Traceability (30 seconds)
- Open `logs/trace.jsonl`
- Show structured logging of all steps
- Highlight timestamp tracking and command extraction

### Key Features Summary (30 seconds)
- ✅ 153 Q&A pairs across 22 categories
- ✅ LoRA fine-tuning with ≤2B parameter model
- ✅ CLI agent with natural language processing
- ✅ Dry-run execution for safety
- ✅ Comprehensive evaluation with metrics
- ✅ Structured logging and traceability

### Conclusion (15 seconds)
- "Complete end-to-end command-line assistant"
- "Ready for deployment and further enhancement"
- "Thank you for watching!"

## Recording Notes

### Screen Recording Setup
1. **Resolution**: 1920x1080 minimum
2. **Frame Rate**: 30 FPS
3. **Audio**: Clear narration with minimal background noise
4. **Duration**: Target 4-5 minutes maximum

### Terminal Setup
- Use clear, readable font (size 14+)
- Dark theme with high contrast
- Clear command prompt
- Slow typing for readability

### File Navigation
- Use file explorer with clear folder structure
- Zoom in on important code sections
- Highlight key lines with cursor

### Demo Flow
1. Start with project overview
2. Show dataset quality and coverage
3. Demonstrate training setup
4. Run live CLI agent demos
5. Show evaluation results
6. Conclude with feature summary

### Technical Requirements
- Record in MP4 format
- Ensure all text is readable
- Include smooth transitions
- Test audio levels before recording
- Have backup demos ready

## Alternative: Loom Recording
If using Loom for recording:
1. Sign up for free Loom account
2. Install Loom desktop app
3. Select "Screen + Camera" recording
4. Follow the script above
5. Edit and export as MP4
6. Ensure video is accessible via private link

## Post-Recording
- Review for clarity and completeness
- Ensure all requirements are demonstrated
- Verify video quality and audio
- Export in appropriate format for submission
