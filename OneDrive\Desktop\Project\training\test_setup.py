#!/usr/bin/env python3
"""
Test script to verify the training setup works correctly.
This script tests model loading, tokenization, and basic training setup
without actually running the full training.
"""

import os
import sys
import json
import torch
from pathlib import Path

# Add parent directory to path to import modules
sys.path.append(str(Path(__file__).parent.parent))

def test_imports():
    """Test if all required packages are available."""
    print("Testing imports...")
    
    try:
        import transformers
        import datasets
        import peft
        import torch
        print(f"✓ transformers: {transformers.__version__}")
        print(f"✓ datasets: {datasets.__version__}")
        print(f"✓ peft: {peft.__version__}")
        print(f"✓ torch: {torch.__version__}")
        print(f"✓ CUDA available: {torch.cuda.is_available()}")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_dataset_loading():
    """Test dataset loading."""
    print("\nTesting dataset loading...")
    
    data_path = "data/command_line_qa.json"
    if not os.path.exists(data_path):
        print(f"❌ Dataset not found: {data_path}")
        return False
    
    try:
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        qa_pairs = data['data']
        print(f"✓ Dataset loaded: {len(qa_pairs)} Q&A pairs")
        print(f"✓ Categories: {len(data['metadata']['categories'])}")
        
        # Test first entry
        if qa_pairs:
            first_pair = qa_pairs[0]
            print(f"✓ Sample question: {first_pair['question'][:50]}...")
            print(f"✓ Sample category: {first_pair['category']}")
        
        return True
    except Exception as e:
        print(f"❌ Dataset loading error: {e}")
        return False

def test_model_loading():
    """Test model and tokenizer loading."""
    print("\nTesting model loading...")
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        
        model_name = "TinyLlama/TinyLlama-1.1B-Chat-v1.0"
        print(f"Testing model: {model_name}")
        
        # Test tokenizer loading
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        print(f"✓ Tokenizer loaded")
        
        # Test model loading (CPU only for testing)
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.float16,
            device_map="cpu"  # Use CPU for testing
        )
        print(f"✓ Model loaded")
        print(f"✓ Model parameters: {model.num_parameters():,}")
        
        # Test tokenization
        test_text = "Question: How do I list files?\nAnswer: Use ls command"
        tokens = tokenizer(test_text, return_tensors="pt")
        print(f"✓ Tokenization test: {len(tokens['input_ids'][0])} tokens")
        
        return True
    except Exception as e:
        print(f"❌ Model loading error: {e}")
        return False

def test_lora_setup():
    """Test LoRA configuration."""
    print("\nTesting LoRA setup...")
    
    try:
        from peft import LoraConfig, TaskType
        
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=16,
            lora_alpha=32,
            lora_dropout=0.1,
            target_modules=["q_proj", "v_proj", "k_proj", "o_proj"]
        )
        print(f"✓ LoRA config created")
        print(f"✓ LoRA rank: {lora_config.r}")
        print(f"✓ LoRA alpha: {lora_config.lora_alpha}")
        
        return True
    except Exception as e:
        print(f"❌ LoRA setup error: {e}")
        return False

def test_training_args():
    """Test training arguments setup."""
    print("\nTesting training arguments...")
    
    try:
        from transformers import TrainingArguments
        
        training_args = TrainingArguments(
            output_dir="./test_output",
            num_train_epochs=1,
            per_device_train_batch_size=1,
            learning_rate=2e-4,
            logging_steps=10,
            save_strategy="epoch",
        )
        print(f"✓ Training arguments created")
        print(f"✓ Output dir: {training_args.output_dir}")
        print(f"✓ Epochs: {training_args.num_train_epochs}")
        print(f"✓ Batch size: {training_args.per_device_train_batch_size}")
        
        return True
    except Exception as e:
        print(f"❌ Training arguments error: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing training setup...\n")
    
    tests = [
        ("Package imports", test_imports),
        ("Dataset loading", test_dataset_loading),
        ("Model loading", test_model_loading),
        ("LoRA setup", test_lora_setup),
        ("Training arguments", test_training_args),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Training setup is ready.")
        print("\nNext steps:")
        print("1. Run: python train.py (for local training)")
        print("2. Or use train.ipynb in Google Colab")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")
        print("Make sure all required packages are installed:")
        print("pip install -r requirements.txt")

if __name__ == "__main__":
    main()
