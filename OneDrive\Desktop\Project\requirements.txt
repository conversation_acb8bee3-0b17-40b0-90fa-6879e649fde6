# Core ML/AI libraries
torch>=2.0.0
transformers>=4.30.0
datasets>=2.12.0
peft>=0.4.0
accelerate>=0.20.0
bitsandbytes>=0.39.0

# Data processing
pandas>=1.5.0
numpy>=1.24.0
jsonlines>=3.1.0

# Evaluation metrics
rouge-score>=0.1.2
nltk>=3.8.1
sacrebleu>=2.3.1

# CLI and utilities
click>=8.1.0
tqdm>=4.65.0
colorama>=0.4.6

# Logging and monitoring
wandb>=0.15.0

# Development tools
jupyter>=1.0.0
notebook>=6.5.0

# Optional: For video recording
opencv-python>=4.7.0
