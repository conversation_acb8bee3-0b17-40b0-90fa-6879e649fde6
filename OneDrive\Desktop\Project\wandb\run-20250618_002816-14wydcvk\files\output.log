  0%|                                                                                                            | 0/13 [00:00<?, ?it/s]`loss_type=None` was set in the config but it is unrecognised.Using the default loss: `ForCausalLMLoss`.
100%|███████████████████████████████████████████████████████████████████████████████████████████████████| 13/13 [00:42<00:00,  3.28s/it]
{'loss': 14.2289, 'grad_norm': 1.6285436153411865, 'learning_rate': 0.0002, 'epoch': 0.4}
{'loss': 13.5181, 'grad_norm': 2.7875313758850098, 'learning_rate': 0.00045000000000000004, 'epoch': 0.8}
{'train_runtime': 45.7954, 'train_samples_per_second': 1.092, 'train_steps_per_second': 0.284, 'train_loss': 14.085135826697716, 'epoch': 1.0}

⏱️ Training completed in: 0:00:46.002866

💾 Saving model...
✅ Model and adapters saved to: training/adapters
🎉 Training completed successfully!

🧪 Testing the fine-tuned model...
The attention mask is not set and cannot be inferred from input because pad token is same as eos token. As a consequence, you may observe unexpected behavior. Please pass your input's `attention_mask` to obtain reliable results.
Test input: Q: How do I create a Git repository?
A:
Model output: Q: How do I create a Git repository?
A: git git grep bgit search commit bcommit git git loggit.com commit git commits git commit git commit git commit git commit commit commit git commit gitCSS git commit
