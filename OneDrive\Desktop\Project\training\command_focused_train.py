#!/usr/bin/env python3
"""
Command-focused training script that specifically trains the model to generate shell commands.
"""

import os
import json
import torch
from datetime import datetime
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from datasets import Dataset
from peft import LoraConfig, get_peft_model, TaskType
import wandb

def create_command_focused_dataset():
    """Create a dataset specifically focused on command generation."""
    
    # Command-focused training examples
    command_examples = [
        {
            "instruction": "Create a new Git branch and switch to it",
            "command": "git checkout -b new-branch"
        },
        {
            "instruction": "List all Python files in current directory", 
            "command": "ls *.py"
        },
        {
            "instruction": "Create a virtual environment",
            "command": "python -m venv myenv"
        },
        {
            "instruction": "Activate virtual environment",
            "command": "source myenv/bin/activate"
        },
        {
            "instruction": "Install packages from requirements",
            "command": "pip install -r requirements.txt"
        },
        {
            "instruction": "Compress folder with tar and gzip",
            "command": "tar -czvf archive.tar.gz folder/"
        },
        {
            "instruction": "Search for text in files recursively",
            "command": "grep -r \"pattern\" ."
        },
        {
            "instruction": "Find files by name",
            "command": "find . -name \"*.txt\""
        },
        {
            "instruction": "Show Git status",
            "command": "git status"
        },
        {
            "instruction": "Add all files to Git",
            "command": "git add ."
        },
        {
            "instruction": "Commit changes with message",
            "command": "git commit -m \"commit message\""
        },
        {
            "instruction": "Push to remote repository",
            "command": "git push origin main"
        },
        {
            "instruction": "Create a new directory",
            "command": "mkdir new-directory"
        },
        {
            "instruction": "Copy files",
            "command": "cp source.txt destination.txt"
        },
        {
            "instruction": "Move files",
            "command": "mv oldname.txt newname.txt"
        },
        {
            "instruction": "Remove files",
            "command": "rm filename.txt"
        },
        {
            "instruction": "Change file permissions",
            "command": "chmod 755 script.sh"
        },
        {
            "instruction": "View file contents",
            "command": "cat filename.txt"
        },
        {
            "instruction": "Show current directory",
            "command": "pwd"
        },
        {
            "instruction": "Change directory",
            "command": "cd /path/to/directory"
        },
        {
            "instruction": "List files with details",
            "command": "ls -la"
        },
        {
            "instruction": "Extract tar archive",
            "command": "tar -xzvf archive.tar.gz"
        },
        {
            "instruction": "Check disk usage",
            "command": "df -h"
        },
        {
            "instruction": "Show running processes",
            "command": "ps aux"
        },
        {
            "instruction": "Kill a process",
            "command": "kill -9 1234"
        }
    ]
    
    # Format for training
    formatted_data = []
    for example in command_examples:
        # Simple instruction -> command format
        text = f"Instruction: {example['instruction']}\nCommand: {example['command']}"
        formatted_data.append({"text": text})
    
    return Dataset.from_list(formatted_data)

def main():
    print("🎯 Command-Focused LoRA Training")
    print("=" * 50)
    
    # Set wandb API key
    wandb.login(key="****************************************")
    
    # Configuration
    MODEL_NAME = "microsoft/DialoGPT-small"
    OUTPUT_DIR = "training/adapters"
    MAX_LENGTH = 128  # Shorter for command focus
    
    print(f"Model: {MODEL_NAME}")
    print(f"Output: {OUTPUT_DIR}")
    
    # Create output directory
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Create command-focused dataset
    print("\n📊 Creating command-focused dataset...")
    dataset = create_command_focused_dataset()
    print(f"Created {len(dataset)} command examples")
    
    # Load model and tokenizer
    print("\n🤖 Loading model and tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
    
    # Add pad token if missing
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    model = AutoModelForCausalLM.from_pretrained(
        MODEL_NAME,
        torch_dtype=torch.float32,
        device_map="auto"
    )
    
    print(f"Model parameters: {model.num_parameters():,}")
    
    # Tokenize dataset
    print("\n🔤 Tokenizing dataset...")
    def tokenize_function(examples):
        return tokenizer(
            examples["text"],
            truncation=True,
            padding="max_length",
            max_length=MAX_LENGTH,
            return_tensors="pt"
        )
    
    tokenized_dataset = dataset.map(
        tokenize_function,
        batched=True,
        remove_columns=dataset.column_names
    )
    
    # Setup LoRA
    print("\n⚙️ Setting up LoRA...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        inference_mode=False,
        r=16,  # Higher rank for better command learning
        lora_alpha=32,
        lora_dropout=0.1,
        target_modules=["c_attn", "c_proj"]
    )
    
    model = get_peft_model(model, lora_config)
    
    trainable_params = model.num_parameters()
    total_params = model.num_parameters(only_trainable=False)
    
    print(f"Trainable parameters: {trainable_params:,}")
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable %: {100 * trainable_params / total_params:.2f}%")
    
    # Training arguments
    print("\n🏋️ Setting up training...")
    training_args = TrainingArguments(
        output_dir=OUTPUT_DIR,
        num_train_epochs=2,  # More epochs for better command learning
        per_device_train_batch_size=4,
        gradient_accumulation_steps=2,
        warmup_steps=20,
        learning_rate=1e-4,  # Lower learning rate for stability
        logging_steps=5,
        save_strategy="epoch",
        eval_strategy="no",
        save_total_limit=1,
        remove_unused_columns=False,
        dataloader_pin_memory=False,
        fp16=False,
        report_to="wandb",
        run_name="command-focused-training",
    )
    
    # Data collator
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )
    
    # Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_dataset,
        data_collator=data_collator,
    )
    
    # Train
    print("\n🎯 Starting command-focused training...")
    start_time = datetime.now()
    
    try:
        trainer.train()
        training_success = True
    except Exception as e:
        print(f"Training error: {e}")
        training_success = False
    
    end_time = datetime.now()
    training_time = end_time - start_time
    
    print(f"\n⏱️ Training completed in: {training_time}")
    
    if training_success:
        # Save the LoRA adapter
        print("\n💾 Saving model...")
        model.save_pretrained(OUTPUT_DIR)
        tokenizer.save_pretrained(OUTPUT_DIR)
        
        # Test the model
        print("\n🧪 Testing command generation...")
        test_prompt = "Instruction: Create a new Git branch\nCommand:"
        inputs = tokenizer(test_prompt, return_tensors="pt")
        
        with torch.no_grad():
            outputs = model.generate(
                inputs.input_ids,
                max_new_tokens=20,
                temperature=0.3,  # Lower temperature for more focused output
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id
            )
        
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        print(f"Test input: {test_prompt}")
        print(f"Model output: {response}")
        
        # Save metadata
        metadata = {
            "model_name": MODEL_NAME,
            "training_time": str(training_time),
            "num_epochs": 2,
            "batch_size": 4,
            "learning_rate": 1e-4,
            "dataset_size": len(dataset),
            "max_length": MAX_LENGTH,
            "trainable_params": trainable_params,
            "total_params": total_params,
            "lora_config": {
                "r": lora_config.r,
                "lora_alpha": lora_config.lora_alpha,
                "lora_dropout": lora_config.lora_dropout,
            },
            "training_date": datetime.now().isoformat(),
            "training_success": True,
            "training_type": "command_focused"
        }
        
        with open(os.path.join(OUTPUT_DIR, "training_metadata.json"), "w") as f:
            json.dump(metadata, f, indent=2)
        
        print(f"✅ Command-focused model saved to: {OUTPUT_DIR}")
        print("🎉 Training completed successfully!")
        
    else:
        print("❌ Training failed")

if __name__ == "__main__":
    main()
