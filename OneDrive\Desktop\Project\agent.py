#!/usr/bin/env python3
"""
Command-Line Assistant Agent

This script accepts natural language instructions and generates step-by-step plans
using a fine-tuned language model. It can execute commands in dry-run mode and
logs all steps to trace.jsonl.

Usage:
    python agent.py "Create a new Git branch and switch to it"
    python agent.py "List all Python files in current directory"
"""

import os
import sys
import json
import argparse
import subprocess
import jsonlines
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel

class CommandLineAgent:
    def __init__(self, model_path: str = None, base_model: str = None, use_base_model: bool = False):
        """
        Initialize the Command Line Agent.

        Args:
            model_path: Path to fine-tuned LoRA adapter
            base_model: Base model name (e.g., microsoft/DialoGPT-small)
            use_base_model: If True, use base model without fine-tuning
        """
        self.model_path = model_path
        self.base_model = base_model or "microsoft/DialoGPT-small"
        self.use_base_model = use_base_model
        self.model = None
        self.tokenizer = None
        self.log_file = "logs/trace.jsonl"
        
        # Ensure logs directory exists
        os.makedirs("logs", exist_ok=True)
        
        self._load_model()
    
    def _load_model(self):
        """Load the model and tokenizer."""
        print(f"Loading model: {self.base_model}")
        
        # Load tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(self.base_model)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # Load base model
        self.model = AutoModelForCausalLM.from_pretrained(
            self.base_model,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True
        )
        
        # Load fine-tuned adapter if available and not using base model
        if not self.use_base_model and self.model_path and os.path.exists(self.model_path):
            print(f"Loading fine-tuned adapter from: {self.model_path}")
            self.model = PeftModel.from_pretrained(self.model, self.model_path)
        elif not self.use_base_model:
            print("⚠️  Fine-tuned model not found, using base model")
        
        print("Model loaded successfully")
    
    def generate_plan(self, instruction: str) -> str:
        """
        Generate a step-by-step plan for the given instruction.
        
        Args:
            instruction: Natural language instruction
            
        Returns:
            Generated plan as string
        """
        # Format the prompt to encourage command generation
        prompt = f"Instruction: {instruction}\nCommand:"
        
        # Tokenize input
        inputs = self.tokenizer(
            prompt,
            return_tensors="pt",
            truncation=True,
            max_length=512
        )
        
        # Generate response
        with torch.no_grad():
            outputs = self.model.generate(
                inputs.input_ids,
                attention_mask=inputs.attention_mask,
                max_new_tokens=256,
                temperature=0.7,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        # Decode response
        full_response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Extract only the generated part (after "Command:")
        if "Command:" in full_response:
            response = full_response.split("Command:", 1)[1].strip()
        elif "Instruction:" in full_response:
            # If the model repeated the instruction, extract after it
            parts = full_response.split("Instruction:", 1)
            if len(parts) > 1:
                response = parts[1].strip()
            else:
                response = full_response.strip()
        else:
            response = full_response.strip()
        
        return response
    
    def parse_commands(self, plan: str) -> List[str]:
        """
        Extract shell commands from the generated plan.
        
        Args:
            plan: Generated plan text
            
        Returns:
            List of shell commands found in the plan
        """
        commands = []
        lines = plan.split('\n')
        
        for line in lines:
            line = line.strip()

            # Skip empty lines and common non-command phrases
            if not line or line.lower().startswith(('why', 'what', 'how about', 'maybe', 'you could', 'try')):
                continue

            # Look for common command patterns
            command_starters = [
                'git ', 'ls ', 'cd ', 'mkdir ', 'cp ', 'mv ', 'rm ', 'chmod ',
                'grep ', 'find ', 'tar ', 'gzip ', 'ssh ', 'scp ', 'docker ',
                'pip ', 'python ', 'npm ', 'curl ', 'wget ', 'sudo ', 'cat ',
                'echo ', 'touch ', 'head ', 'tail ', 'sort ', 'uniq ', 'wc ',
                'source ', 'export ', 'alias ', 'which ', 'man ', 'ps ', 'kill ',
                'df ', 'du ', 'mount ', 'umount ', 'ln ', 'file ', 'stat '
            ]

            if any(line.startswith(cmd) for cmd in command_starters):
                commands.append(line)

            # Also check if line contains command-like patterns
            elif any(cmd.strip() in line for cmd in command_starters):
                # Extract potential command from the line
                for cmd in command_starters:
                    if cmd.strip() in line:
                        # Try to extract the command part
                        if line.startswith(cmd.strip()):
                            commands.append(line)
                            break
            
            # Look for commands in code blocks or after "Use:"
            if line.startswith("Use: "):
                cmd = line[5:].strip()
                if cmd:
                    commands.append(cmd)
            
            # Look for commands in backticks
            if '`' in line:
                import re
                cmd_matches = re.findall(r'`([^`]+)`', line)
                for cmd in cmd_matches:
                    if any(cmd.strip().startswith(c) for c in ['git', 'ls', 'cd', 'mkdir']):
                        commands.append(cmd.strip())
        
        return commands
    
    def execute_dry_run(self, command: str) -> Dict:
        """
        Execute command in dry-run mode (just echo the command).
        
        Args:
            command: Shell command to execute
            
        Returns:
            Execution result dictionary
        """
        print(f"Dry-run: {command}")
        
        result = {
            "command": command,
            "dry_run": True,
            "timestamp": datetime.now().isoformat(),
            "status": "dry_run_executed"
        }
        
        return result
    
    def log_step(self, step_data: Dict):
        """
        Log a step to the trace file.
        
        Args:
            step_data: Dictionary containing step information
        """
        with jsonlines.open(self.log_file, mode='a') as writer:
            writer.write(step_data)
    
    def process_instruction(self, instruction: str) -> Dict:
        """
        Process a natural language instruction end-to-end.
        
        Args:
            instruction: Natural language instruction
            
        Returns:
            Processing result dictionary
        """
        print(f"Instruction: {instruction}")

        # Log the initial instruction
        initial_log = {
            "type": "instruction",
            "content": instruction,
            "timestamp": datetime.now().isoformat()
        }
        self.log_step(initial_log)

        # Generate plan
        print("Generating plan...")
        plan = self.generate_plan(instruction)
        print(f"Generated plan:\n{plan}")

        # Log the generated plan
        plan_log = {
            "type": "plan",
            "content": plan,
            "timestamp": datetime.now().isoformat()
        }
        self.log_step(plan_log)

        # Extract commands
        commands = self.parse_commands(plan)
        print(f"Extracted {len(commands)} commands")
        
        # Execute commands in dry-run mode
        execution_results = []
        for i, command in enumerate(commands, 1):
            print(f"\n--- Step {i} ---")
            result = self.execute_dry_run(command)
            execution_results.append(result)
            self.log_step(result)
        
        # Summary
        summary = {
            "type": "summary",
            "instruction": instruction,
            "plan": plan,
            "commands_found": len(commands),
            "commands": commands,
            "timestamp": datetime.now().isoformat()
        }
        self.log_step(summary)
        
        return summary

def main():
    parser = argparse.ArgumentParser(
        description="Command-Line Assistant Agent",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python agent.py "Create a new Git branch and switch to it"
  python agent.py "List all Python files in current directory"
  python agent.py "Compress a folder using tar"
  python agent.py "Set up a virtual environment"
        """
    )
    
    parser.add_argument(
        "instruction",
        help="Natural language instruction for the command-line task"
    )
    parser.add_argument(
        "--model-path",
        default="training/adapters",
        help="Path to fine-tuned LoRA adapter (default: training/adapters)"
    )
    parser.add_argument(
        "--base-model",
        default="microsoft/DialoGPT-small",
        help="Base model name (default: microsoft/DialoGPT-small)"
    )
    parser.add_argument(
        "--use-base-model",
        action="store_true",
        help="Use base model without fine-tuning (for comparison)"
    )
    
    args = parser.parse_args()
    
    print("Command-Line Assistant Agent")
    print("=" * 50)
    
    try:
        # Initialize agent
        agent = CommandLineAgent(
            model_path=args.model_path,
            base_model=args.base_model,
            use_base_model=args.use_base_model
        )
        
        # Process instruction
        result = agent.process_instruction(args.instruction)
        
        print("\n" + "=" * 50)
        print("Processing completed!")
        print(f"Commands found: {result['commands_found']}")
        print(f"Log file: {agent.log_file}")

        if result['commands']:
            print("\nCommands that would be executed:")
            for i, cmd in enumerate(result['commands'], 1):
                print(f"  {i}. {cmd}")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
