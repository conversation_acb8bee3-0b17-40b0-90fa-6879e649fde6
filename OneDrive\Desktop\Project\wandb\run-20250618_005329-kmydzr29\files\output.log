  0%|                                                                                             | 0/8 [00:00<?, ?it/s]`loss_type=None` was set in the config but it is unrecognised.Using the default loss: `ForCausalLMLoss`.
100%|█████████████████████████████████████████████████████████████████████████████████████| 8/8 [00:38<00:00,  4.83s/it]
{'loss': 12.9989, 'grad_norm': 4.599514007568359, 'learning_rate': 2e-05, 'epoch': 1.29}
{'train_runtime': 40.1829, 'train_samples_per_second': 1.244, 'train_steps_per_second': 0.199, 'train_loss': 13.09624433517456, 'epoch': 2.0}

⏱️ Training completed in: 0:00:40.499350

💾 Saving model...

🧪 Testing command generation...
The attention mask is not set and cannot be inferred from input because pad token is same as eos token. As a consequence, you may observe unexpected behavior. Please pass your input's `attention_mask` to obtain reliable results.
Test input: Instruction: Create a new Git branch
Command:
Model output: Instruction: Create a new Git branch
Command: command Change the branch
✅ Command-focused model saved to: training/adapters
🎉 Training completed successfully!
