#!/usr/bin/env python3
"""
Test prompts for evaluating the command-line assistant.
Contains the 5 required test prompts plus 2 additional edge cases.
"""

# Required test prompts (as mentioned in requirements)
REQUIRED_TEST_PROMPTS = [
    {
        "id": "test_1",
        "prompt": "Create a new Git branch and switch to it",
        "category": "git",
        "expected_commands": ["git checkout -b <branch-name>", "git branch <branch-name>", "git checkout <branch-name>"],
        "description": "Basic Git branch creation and switching"
    },
    {
        "id": "test_2", 
        "prompt": "List all Python files in the current directory",
        "category": "bash",
        "expected_commands": ["ls *.py", "find . -name \"*.py\"", "ls -la *.py"],
        "description": "File listing with pattern matching"
    },
    {
        "id": "test_3",
        "prompt": "Create a virtual environment and activate it",
        "category": "venv",
        "expected_commands": ["python -m venv myenv", "source myenv/bin/activate"],
        "description": "Python virtual environment setup"
    },
    {
        "id": "test_4",
        "prompt": "Compress a folder using tar with gzip",
        "category": "tar",
        "expected_commands": ["tar -czvf archive.tar.gz folder/", "tar -czf archive.tar.gz folder/"],
        "description": "Archive creation with compression"
    },
    {
        "id": "test_5",
        "prompt": "Search for text 'TODO' in all files recursively",
        "category": "grep",
        "expected_commands": ["grep -r \"TODO\" .", "grep -rn \"TODO\" ."],
        "description": "Recursive text search"
    }
]

# Additional edge case test prompts
EDGE_CASE_PROMPTS = [
    {
        "id": "edge_1",
        "prompt": "Set up a complete development environment with Git, virtual environment, and install dependencies",
        "category": "complex",
        "expected_commands": [
            "git init", "python -m venv venv", "source venv/bin/activate", 
            "pip install -r requirements.txt", "git add .", "git commit -m"
        ],
        "description": "Complex multi-step development setup"
    },
    {
        "id": "edge_2",
        "prompt": "Find and delete all log files older than 7 days",
        "category": "advanced",
        "expected_commands": ["find . -name \"*.log\" -mtime +7 -delete", "find . -name \"*.log\" -mtime +7 -exec rm {} \\;"],
        "description": "Advanced file operations with conditions"
    }
]

# Combined test suite
ALL_TEST_PROMPTS = REQUIRED_TEST_PROMPTS + EDGE_CASE_PROMPTS

def get_test_prompts():
    """Return all test prompts."""
    return ALL_TEST_PROMPTS

def get_required_prompts():
    """Return only the required test prompts."""
    return REQUIRED_TEST_PROMPTS

def get_edge_case_prompts():
    """Return only the edge case prompts."""
    return EDGE_CASE_PROMPTS

def get_prompt_by_id(prompt_id: str):
    """Get a specific prompt by ID."""
    for prompt in ALL_TEST_PROMPTS:
        if prompt["id"] == prompt_id:
            return prompt
    return None

if __name__ == "__main__":
    print("Test Prompts for Command-Line Assistant")
    print("=" * 50)
    
    print(f"\nRequired Test Prompts ({len(REQUIRED_TEST_PROMPTS)}):")
    for i, prompt in enumerate(REQUIRED_TEST_PROMPTS, 1):
        print(f"{i}. {prompt['prompt']} ({prompt['category']})")
    
    print(f"\nEdge Case Prompts ({len(EDGE_CASE_PROMPTS)}):")
    for i, prompt in enumerate(EDGE_CASE_PROMPTS, 1):
        print(f"{i}. {prompt['prompt']} ({prompt['category']})")
    
    print(f"\nTotal: {len(ALL_TEST_PROMPTS)} test prompts")
