#!/usr/bin/env python3
"""
Demo version of the Command-Line Assistant Agent.
This simulates the agent functionality without requiring model download.
Used for demonstration and testing purposes.
"""

import os
import sys
import json
import argparse
import jsonlines
from datetime import datetime
from typing import List, Dict

class DemoCommandLineAgent:
    """Demo version of the command line agent with simulated responses."""
    
    def __init__(self):
        self.log_file = "logs/trace.jsonl"
        os.makedirs("logs", exist_ok=True)
        
        # Predefined responses for demo purposes
        self.demo_responses = {
            "create a new git branch and switch to it": {
                "plan": "To create a new Git branch and switch to it, you can use the following steps:\n\n1. Create a new branch: git branch <branch-name>\n2. Switch to the new branch: git checkout <branch-name>\n\nAlternatively, you can do both in one command:\ngit checkout -b <branch-name>\n\nThis creates and switches to the new branch in one step.",
                "commands": ["git checkout -b <branch-name>", "git branch <branch-name>", "git checkout <branch-name>"]
            },
            "list all python files in current directory": {
                "plan": "To list all Python files in the current directory, you can use:\n\n1. Simple listing: ls *.py\n2. Detailed listing: ls -la *.py\n3. Recursive search: find . -name \"*.py\"\n\nThe find command is most comprehensive as it searches subdirectories too.",
                "commands": ["ls *.py", "ls -la *.py", "find . -name \"*.py\""]
            },
            "create a virtual environment and activate it": {
                "plan": "To create and activate a Python virtual environment:\n\n1. Create virtual environment: python -m venv myenv\n2. Activate on Linux/Mac: source myenv/bin/activate\n3. Activate on Windows: myenv\\Scripts\\activate\n\nAfter activation, you'll see (myenv) in your prompt.",
                "commands": ["python -m venv myenv", "source myenv/bin/activate", "myenv\\Scripts\\activate"]
            },
            "compress a folder using tar with gzip": {
                "plan": "To compress a folder using tar with gzip compression:\n\n1. Basic compression: tar -czvf archive.tar.gz folder/\n2. Alternative syntax: tar -czf archive.tar.gz folder/\n\nFlags explanation:\n-c: create archive\n-z: gzip compression\n-v: verbose output\n-f: specify filename",
                "commands": ["tar -czvf archive.tar.gz folder/", "tar -czf archive.tar.gz folder/"]
            },
            "search for text todo in all files recursively": {
                "plan": "To search for 'TODO' text in all files recursively:\n\n1. Basic recursive search: grep -r \"TODO\" .\n2. With line numbers: grep -rn \"TODO\" .\n3. Case insensitive: grep -ri \"TODO\" .\n4. Specific file types: grep -r \"TODO\" --include=\"*.py\" .\n\nThe -r flag makes it recursive through subdirectories.",
                "commands": ["grep -r \"TODO\" .", "grep -rn \"TODO\" .", "grep -ri \"TODO\" ."]
            },
            "set up a complete development environment with git virtual environment and install dependencies": {
                "plan": "To set up a complete development environment:\n\n1. Initialize Git repository: git init\n2. Create virtual environment: python -m venv venv\n3. Activate virtual environment: source venv/bin/activate\n4. Install dependencies: pip install -r requirements.txt\n5. Create initial commit: git add . && git commit -m \"Initial commit\"\n\nThis creates a full development setup with version control and isolated Python environment.",
                "commands": ["git init", "python -m venv venv", "source venv/bin/activate", "pip install -r requirements.txt", "git add .", "git commit -m \"Initial commit\""]
            },
            "find and delete all log files older than 7 days": {
                "plan": "To find and delete log files older than 7 days:\n\n1. Find and delete: find . -name \"*.log\" -mtime +7 -delete\n2. Alternative with exec: find . -name \"*.log\" -mtime +7 -exec rm {} \\;\n3. Preview first: find . -name \"*.log\" -mtime +7\n\nThe -mtime +7 finds files modified more than 7 days ago.",
                "commands": ["find . -name \"*.log\" -mtime +7 -delete", "find . -name \"*.log\" -mtime +7 -exec rm {} \\;", "find . -name \"*.log\" -mtime +7"]
            }
        }
    
    def generate_plan(self, instruction: str) -> str:
        """Generate a plan for the given instruction."""
        instruction_lower = instruction.lower().strip()
        
        # Find matching demo response
        for key, response in self.demo_responses.items():
            if key in instruction_lower or instruction_lower in key:
                return response["plan"]
        
        # Default response for unknown instructions
        return f"I understand you want to: {instruction}\n\nHere's a general approach:\n1. Break down the task into smaller steps\n2. Use appropriate command-line tools\n3. Test commands safely before execution\n\nFor specific guidance, please provide more details about your environment and requirements."
    
    def parse_commands(self, plan: str, instruction: str = "") -> List[str]:
        """Extract commands from the plan."""
        instruction_lower = instruction.lower().strip()

        # Find matching demo response based on original instruction
        for key, response in self.demo_responses.items():
            if key in instruction_lower or instruction_lower in key:
                return response["commands"]
        
        # Basic command extraction for unknown plans
        commands = []
        lines = plan.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # Look for common command patterns
            if any(line.startswith(cmd) for cmd in [
                'git ', 'ls ', 'cd ', 'mkdir ', 'cp ', 'mv ', 'rm ', 'chmod ',
                'grep ', 'find ', 'tar ', 'gzip ', 'ssh ', 'scp ', 'docker ',
                'pip ', 'python ', 'npm ', 'curl ', 'wget ', 'sudo ', 'cat '
            ]):
                commands.append(line)
        
        return commands
    
    def execute_dry_run(self, command: str) -> Dict:
        """Execute command in dry-run mode."""
        print(f"🔍 Dry-run: {command}")
        
        result = {
            "command": command,
            "dry_run": True,
            "timestamp": datetime.now().isoformat(),
            "status": "dry_run_executed"
        }
        
        return result
    
    def log_step(self, step_data: Dict):
        """Log a step to the trace file."""
        with jsonlines.open(self.log_file, mode='a') as writer:
            writer.write(step_data)
    
    def process_instruction(self, instruction: str) -> Dict:
        """Process a natural language instruction end-to-end."""
        print(f"📝 Instruction: {instruction}")
        
        # Log the initial instruction
        initial_log = {
            "type": "instruction",
            "content": instruction,
            "timestamp": datetime.now().isoformat(),
            "demo_mode": True
        }
        self.log_step(initial_log)
        
        # Generate plan
        print("🤖 Generating plan...")
        plan = self.generate_plan(instruction)
        print(f"📋 Generated plan:\n{plan}")
        
        # Log the generated plan
        plan_log = {
            "type": "plan",
            "content": plan,
            "timestamp": datetime.now().isoformat(),
            "demo_mode": True
        }
        self.log_step(plan_log)
        
        # Extract commands
        commands = self.parse_commands(plan, instruction)
        print(f"🔧 Extracted {len(commands)} commands")
        
        # Execute commands in dry-run mode
        execution_results = []
        for i, command in enumerate(commands, 1):
            print(f"\n--- Step {i} ---")
            result = self.execute_dry_run(command)
            execution_results.append(result)
            self.log_step(result)
        
        # Summary
        summary = {
            "type": "summary",
            "instruction": instruction,
            "plan": plan,
            "commands_found": len(commands),
            "commands": commands,
            "timestamp": datetime.now().isoformat(),
            "demo_mode": True
        }
        self.log_step(summary)
        
        return summary

def main():
    parser = argparse.ArgumentParser(
        description="Demo Command-Line Assistant Agent",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Demo Examples:
  python demo_agent.py "Create a new Git branch and switch to it"
  python demo_agent.py "List all Python files in current directory"
  python demo_agent.py "Compress a folder using tar"
  python demo_agent.py "Set up a virtual environment"
        """
    )
    
    parser.add_argument(
        "instruction",
        help="Natural language instruction for the command-line task"
    )
    
    args = parser.parse_args()
    
    print("🚀 Demo Command-Line Assistant Agent")
    print("=" * 50)
    print("⚠️  This is a demo version with simulated responses")
    print("=" * 50)
    
    try:
        # Initialize demo agent
        agent = DemoCommandLineAgent()
        
        # Process instruction
        result = agent.process_instruction(args.instruction)
        
        print("\n" + "=" * 50)
        print("✅ Processing completed!")
        print(f"📊 Commands found: {result['commands_found']}")
        print(f"📝 Log file: {agent.log_file}")
        
        if result['commands']:
            print("\n🔧 Commands that would be executed:")
            for i, cmd in enumerate(result['commands'], 1):
                print(f"  {i}. {cmd}")
        
        print("\n💡 Note: This is a demo. The actual agent uses a fine-tuned language model.")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
