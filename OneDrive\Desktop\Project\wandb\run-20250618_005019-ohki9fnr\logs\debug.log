2025-06-18 00:50:19,396 INFO    MainThread:23464 [wandb_setup.py:_flush():81] Current SDK version is 0.20.1
2025-06-18 00:50:19,396 INFO    MainThread:23464 [wandb_setup.py:_flush():81] Configure stats pid to 23464
2025-06-18 00:50:19,396 INFO    MainThread:23464 [wandb_setup.py:_flush():81] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-06-18 00:50:19,396 INFO    MainThread:23464 [wandb_setup.py:_flush():81] Loading settings from C:\Users\<USER>\OneDrive\Desktop\Project\wandb\settings
2025-06-18 00:50:19,396 INFO    MainThread:23464 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-06-18 00:50:19,396 INFO    MainThread:23464 [wandb_init.py:setup_run_log_directory():703] Logging user logs to C:\Users\<USER>\OneDrive\Desktop\Project\wandb\run-20250618_005019-ohki9fnr\logs\debug.log
2025-06-18 00:50:19,396 INFO    MainThread:23464 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to C:\Users\<USER>\OneDrive\Desktop\Project\wandb\run-20250618_005019-ohki9fnr\logs\debug-internal.log
2025-06-18 00:50:19,396 INFO    MainThread:23464 [wandb_init.py:init():831] calling init triggers
2025-06-18 00:50:19,396 INFO    MainThread:23464 [wandb_init.py:init():836] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-06-18 00:50:19,396 INFO    MainThread:23464 [wandb_init.py:init():872] starting backend
2025-06-18 00:50:19,619 INFO    MainThread:23464 [wandb_init.py:init():875] sending inform_init request
2025-06-18 00:50:19,659 INFO    MainThread:23464 [wandb_init.py:init():883] backend started and connected
2025-06-18 00:50:19,660 INFO    MainThread:23464 [wandb_init.py:init():956] updated telemetry
2025-06-18 00:50:19,662 INFO    MainThread:23464 [wandb_init.py:init():980] communicating run to backend with 90.0 second timeout
2025-06-18 00:50:20,605 INFO    MainThread:23464 [wandb_init.py:init():1032] starting run threads in backend
2025-06-18 00:50:20,735 INFO    MainThread:23464 [wandb_run.py:_console_start():2453] atexit reg
2025-06-18 00:50:20,735 INFO    MainThread:23464 [wandb_run.py:_redirect():2301] redirect: wrap_raw
2025-06-18 00:50:20,735 INFO    MainThread:23464 [wandb_run.py:_redirect():2370] Wrapping output streams.
2025-06-18 00:50:20,735 INFO    MainThread:23464 [wandb_run.py:_redirect():2393] Redirects installed.
2025-06-18 00:50:20,737 INFO    MainThread:23464 [wandb_init.py:init():1078] run started, returning control to user process
2025-06-18 00:50:20,737 INFO    MainThread:23464 [wandb_run.py:_config_callback():1358] config_cb None None {'peft_config': {'default': {'task_type': <TaskType.CAUSAL_LM: 'CAUSAL_LM'>, 'peft_type': <PeftType.LORA: 'LORA'>, 'auto_mapping': None, 'base_model_name_or_path': 'microsoft/DialoGPT-small', 'revision': None, 'inference_mode': False, 'r': 8, 'target_modules': {'c_attn', 'c_proj'}, 'exclude_modules': None, 'lora_alpha': 16, 'lora_dropout': 0.1, 'fan_in_fan_out': True, 'bias': 'none', 'use_rslora': False, 'modules_to_save': None, 'init_lora_weights': True, 'layers_to_transform': None, 'layers_pattern': None, 'rank_pattern': {}, 'alpha_pattern': {}, 'megatron_config': None, 'megatron_core': 'megatron.core', 'trainable_token_indices': None, 'loftq_config': {}, 'eva_config': None, 'corda_config': None, 'use_dora': False, 'layer_replication': None, 'runtime_config': {'ephemeral_gpu_offload': False}, 'lora_bias': False}}, 'vocab_size': 50257, 'n_positions': 1024, 'n_embd': 768, 'n_layer': 12, 'n_head': 12, 'n_inner': None, 'activation_function': 'gelu_new', 'resid_pdrop': 0.1, 'embd_pdrop': 0.1, 'attn_pdrop': 0.1, 'layer_norm_epsilon': 1e-05, 'initializer_range': 0.02, 'summary_type': 'cls_index', 'summary_use_proj': True, 'summary_activation': None, 'summary_first_dropout': 0.1, 'summary_proj_to_labels': True, 'scale_attn_weights': True, 'use_cache': True, 'scale_attn_by_inverse_layer_idx': False, 'reorder_and_upcast_attn': False, 'bos_token_id': 50256, 'eos_token_id': 50256, 'return_dict': True, 'output_hidden_states': False, 'output_attentions': False, 'torchscript': False, 'torch_dtype': 'float32', 'use_bfloat16': False, 'tf_legacy_loss': False, 'pruned_heads': {}, 'tie_word_embeddings': True, 'chunk_size_feed_forward': 0, 'is_encoder_decoder': False, 'is_decoder': False, 'cross_attention_hidden_size': None, 'add_cross_attention': False, 'tie_encoder_decoder': False, 'max_length': 20, 'min_length': 0, 'do_sample': False, 'early_stopping': False, 'num_beams': 1, 'num_beam_groups': 1, 'diversity_penalty': 0.0, 'temperature': 1.0, 'top_k': 50, 'top_p': 1.0, 'typical_p': 1.0, 'repetition_penalty': 1.0, 'length_penalty': 1.0, 'no_repeat_ngram_size': 0, 'encoder_no_repeat_ngram_size': 0, 'bad_words_ids': None, 'num_return_sequences': 1, 'output_scores': False, 'return_dict_in_generate': False, 'forced_bos_token_id': None, 'forced_eos_token_id': None, 'remove_invalid_values': False, 'exponential_decay_length_penalty': None, 'suppress_tokens': None, 'begin_suppress_tokens': None, 'architectures': ['GPT2LMHeadModel'], 'finetuning_task': None, 'id2label': {0: 'LABEL_0', 1: 'LABEL_1'}, 'label2id': {'LABEL_0': 0, 'LABEL_1': 1}, 'tokenizer_class': None, 'prefix': None, 'pad_token_id': None, 'sep_token_id': None, 'decoder_start_token_id': None, 'task_specific_params': {'conversational': {'max_length': 1000}}, 'problem_type': None, '_name_or_path': 'microsoft/DialoGPT-small', 'transformers_version': '4.52.4', 'model_type': 'gpt2', 'n_ctx': 1024, 'output_dir': 'training/adapters', 'overwrite_output_dir': False, 'do_train': False, 'do_eval': False, 'do_predict': False, 'eval_strategy': 'no', 'prediction_loss_only': False, 'per_device_train_batch_size': 2, 'per_device_eval_batch_size': 8, 'per_gpu_train_batch_size': None, 'per_gpu_eval_batch_size': None, 'gradient_accumulation_steps': 2, 'eval_accumulation_steps': None, 'eval_delay': 0, 'torch_empty_cache_steps': None, 'learning_rate': 0.0005, 'weight_decay': 0.0, 'adam_beta1': 0.9, 'adam_beta2': 0.999, 'adam_epsilon': 1e-08, 'max_grad_norm': 1.0, 'num_train_epochs': 1, 'max_steps': -1, 'lr_scheduler_type': 'linear', 'lr_scheduler_kwargs': {}, 'warmup_ratio': 0.0, 'warmup_steps': 10, 'log_level': 'passive', 'log_level_replica': 'warning', 'log_on_each_node': True, 'logging_dir': 'training/adapters\\runs\\Jun18_00-50-19_SAIKIRAN', 'logging_strategy': 'steps', 'logging_first_step': False, 'logging_steps': 5, 'logging_nan_inf_filter': True, 'save_strategy': 'epoch', 'save_steps': 500, 'save_total_limit': 1, 'save_safetensors': True, 'save_on_each_node': False, 'save_only_model': False, 'restore_callback_states_from_checkpoint': False, 'no_cuda': False, 'use_cpu': False, 'use_mps_device': False, 'seed': 42, 'data_seed': None, 'jit_mode_eval': False, 'use_ipex': False, 'bf16': False, 'fp16': False, 'fp16_opt_level': 'O1', 'half_precision_backend': 'auto', 'bf16_full_eval': False, 'fp16_full_eval': False, 'tf32': None, 'local_rank': 0, 'ddp_backend': None, 'tpu_num_cores': None, 'tpu_metrics_debug': False, 'debug': [], 'dataloader_drop_last': False, 'eval_steps': None, 'dataloader_num_workers': 0, 'dataloader_prefetch_factor': None, 'past_index': -1, 'run_name': 'command-line-assistant-lora', 'disable_tqdm': False, 'remove_unused_columns': False, 'label_names': None, 'load_best_model_at_end': False, 'metric_for_best_model': None, 'greater_is_better': None, 'ignore_data_skip': False, 'fsdp': [], 'fsdp_min_num_params': 0, 'fsdp_config': {'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False}, 'fsdp_transformer_layer_cls_to_wrap': None, 'accelerator_config': {'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None}, 'deepspeed': None, 'label_smoothing_factor': 0.0, 'optim': 'adamw_torch', 'optim_args': None, 'adafactor': False, 'group_by_length': False, 'length_column_name': 'length', 'report_to': ['wandb'], 'ddp_find_unused_parameters': None, 'ddp_bucket_cap_mb': None, 'ddp_broadcast_buffers': None, 'dataloader_pin_memory': False, 'dataloader_persistent_workers': False, 'skip_memory_metrics': True, 'use_legacy_prediction_loop': False, 'push_to_hub': False, 'resume_from_checkpoint': None, 'hub_model_id': None, 'hub_strategy': 'every_save', 'hub_token': '<HUB_TOKEN>', 'hub_private_repo': None, 'hub_always_push': False, 'gradient_checkpointing': False, 'gradient_checkpointing_kwargs': None, 'include_inputs_for_metrics': False, 'include_for_metrics': [], 'eval_do_concat_batches': True, 'fp16_backend': 'auto', 'push_to_hub_model_id': None, 'push_to_hub_organization': None, 'push_to_hub_token': '<PUSH_TO_HUB_TOKEN>', 'mp_parameters': '', 'auto_find_batch_size': False, 'full_determinism': False, 'torchdynamo': None, 'ray_scope': 'last', 'ddp_timeout': 1800, 'torch_compile': False, 'torch_compile_backend': None, 'torch_compile_mode': None, 'include_tokens_per_second': False, 'include_num_input_tokens_seen': False, 'neftune_noise_alpha': None, 'optim_target_modules': None, 'batch_eval_metrics': False, 'eval_on_start': False, 'use_liger_kernel': False, 'eval_use_gather_object': False, 'average_tokens_across_devices': False}
2025-06-18 00:50:20,740 INFO    MainThread:23464 [wandb_config.py:__setitem__():154] [no run ID] config set model/num_parameters = 125250816 - <bound method Run._config_callback of <wandb.sdk.wandb_run.Run object at 0x0000029F3234F050>>
2025-06-18 00:50:20,740 INFO    MainThread:23464 [wandb_run.py:_config_callback():1358] config_cb model/num_parameters 125250816 None
2025-06-18 00:51:30,255 INFO    MsgRouterThr:23464 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
