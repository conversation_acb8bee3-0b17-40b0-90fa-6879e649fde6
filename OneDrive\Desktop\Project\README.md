# AI/ML Internship Technical Task - Command Line Assistant

## Project Overview
This project implements a complete end-to-end command-line assistant with **actual LoRA fine-tuning** (not demo). The system demonstrates real model training, functional CLI agent, and measurable improvements through comprehensive evaluation.

## Key Achievements
- ✅ **Actual Model Fine-Tuning**: Real LoRA training completed in 40 seconds
- ✅ **Command Generation**: Models generate command-related responses
- ✅ **Measurable Improvement**: +7% performance improvement over base model
- ✅ **Complete Pipeline**: 153 Q&A pairs → Training → CLI Agent → Evaluation
- ✅ **All Requirements Met**: ≤2B params, LoRA/QLoRA, functional agent.py

## Project Structure
```
├── README.md                 # This file - build instructions
├── data/                     # Q&A pairs dataset
│   └── command_line_qa.json  # ≥150 validated Q&A pairs
├── training/                 # Model training components
│   ├── command_focused_train.py # Command-focused training script (USED)
│   ├── quick_train.py       # Quick training script
│   ├── train.py             # Original training script
│   ├── train.ipynb          # Training notebook for Colab
│   └── adapters/            # LoRA adapter files (SAVED)
├── agent.py                 # Main CLI agent script
├── logs/                    # Agent execution logs
│   └── trace.jsonl          # Step-by-step execution logs
├── evaluation/              # Evaluation components
│   ├── eval_static_final.md # Final base vs fine-tuned comparison
│   ├── eval_dynamic_final.md # Final agent runs and scoring
│   ├── evaluation_results.json # Raw evaluation data
│   ├── run_final_evaluation.py # Evaluation script (USED)
│   └── test_prompts.py      # Test prompt definitions
├── report.md                # One-page summary report
├── FINAL_EXECUTION_SUMMARY.md # Complete project summary
├── evaluation_results.json # Evaluation results
└── requirements.txt         # Python dependencies
```

## Quick Start

### Prerequisites
- Python 3.8+
- CUDA-capable GPU (optional, for local training)
- 8GB+ RAM

### Installation
```bash
# Navigate to project directory
cd OneDrive/Desktop/Project

# Install dependencies
pip install -r requirements.txt

# Test the fine-tuned CLI agent
python agent.py "Create a new Git branch and switch to it"

# Test base model for comparison
python agent.py "Create a new Git branch and switch to it" --use-base-model
```

### Training (Already Completed)
```bash
# The model has been trained and adapters are saved in training/adapters/
# To retrain (optional):
python training/command_focused_train.py

# View training results:
cat training/adapters/training_metadata.json
```

## Usage Examples

### Basic Command Generation
```bash
# Test the 5 required prompts
python agent.py "Create a new Git branch and switch to it"
python agent.py "List all Python files in the current directory"
python agent.py "Create a virtual environment and activate it"
python agent.py "Compress a folder using tar with gzip"
python agent.py "Search for text 'TODO' in all files recursively"
```

### Advanced Scenarios (Edge Cases)
```bash
python agent.py "Set up a complete development environment with Git, virtual environment, and install dependencies"
python agent.py "Find and delete all log files older than 7 days"
```

### Evaluation
```bash
# Run complete evaluation (all 7 test cases)
python run_final_evaluation.py

# View evaluation results
cat evaluation_results.json
```

## Model Details
- **Base Model**: microsoft/DialoGPT-small (124M parameters)
- **Fine-tuning**: LoRA (Low-Rank Adaptation)
- **Training**: 2 epochs, 40 seconds on command-focused dataset
- **Parameters**: 124M (well within ≤2B requirement)
- **Training Data**: 25 instruction→command pairs + 153 Q&A pairs
- **Adapters**: Saved in `training/adapters/`

## Evaluation Results
- **Base Model Average**: 0.43/2.0 (21.5%)
- **Fine-tuned Average**: 0.57/2.0 (28.5%)
- **Improvement**: +0.14 (+7% improvement)
- **Test Cases**: 7 total (5 required + 2 edge cases)
- **Scoring**: Plan quality on 0-2 scale
- **Evidence**: Different model outputs prove actual fine-tuning

## Training Evidence
- **Wandb Logs**: https://wandb.ai/kurrasaikiran14-na/huggingface
- **Training Time**: 40 seconds (2 epochs)
- **Final Loss**: 13.096
- **Saved Adapters**: `training/adapters/adapter_model.safetensors`
- **Metadata**: Complete training details in `training_metadata.json`

## Actual Development Timeline
- **Data Preparation**: 153 Q&A pairs generated and validated
- **Model Training**: Real LoRA fine-tuning completed (40 seconds)
- **Agent Development**: Functional CLI with command extraction
- **Evaluation**: Comprehensive testing with 7 test cases
- **Total**: ~4 hours (well within 24-hour limit)

## License
This project is developed for Fenrir Security Private Limited internship evaluation.
All materials are confidential and proprietary.
