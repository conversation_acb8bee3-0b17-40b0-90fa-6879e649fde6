# AI/ML Internship Technical Task - Command Line Assistant

## Project Overview
This project implements a mini end-to-end demo for a command-line assistant using fine-tuned language models. The system includes data preparation, model fine-tuning with LoRA/QLoRA, CLI agent development, and comprehensive evaluation.

## Project Structure
```
├── README.md                 # This file - build instructions
├── data/                     # Q&A pairs dataset
│   └── command_line_qa.json  # ≥150 validated Q&A pairs
├── training/                 # Model training components
│   ├── train.py             # Training script
│   ├── train.ipynb          # Training notebook for Colab
│   └── adapters/            # LoRA adapter files
├── agent.py                 # Main CLI agent script
├── logs/                    # Agent execution logs
│   └── trace.jsonl          # Step-by-step execution logs
├── evaluation/              # Evaluation components
│   ├── eval_static.md       # Base vs fine-tuned comparison
│   ├── eval_dynamic.md      # Agent runs and scoring
│   └── test_prompts.py      # Test prompt definitions
├── report.md                # One-page summary report
└── requirements.txt         # Python dependencies
```

## Quick Start

### Prerequisites
- Python 3.8+
- CUDA-capable GPU (optional, for local training)
- 8GB+ RAM

### Installation
```bash
# Clone and navigate to project
cd Project

# Install dependencies
pip install -r requirements.txt

# Run the CLI agent
python agent.py "Create a new Git branch and switch to it"
```

### Training (Optional)
```bash
# Local training
python training/train.py

# Or use the Colab notebook: training/train.ipynb
```

## Usage Examples

### Basic Command Generation
```bash
python agent.py "List all files in current directory"
python agent.py "Create a virtual environment named myenv"
python agent.py "Compress a folder using tar"
```

### Advanced Scenarios
```bash
python agent.py "Set up a Git repository and make initial commit"
python agent.py "Find all Python files containing 'import pandas'"
```

## Model Details
- Base Model: TinyLlama-1.1B (or Phi-2)
- Fine-tuning: LoRA/QLoRA
- Training: 1 epoch on command-line Q&A dataset
- Parameters: ≤2B as per requirements

## Evaluation
- Static: BLEU/ROUGE-L metrics comparing base vs fine-tuned
- Dynamic: Plan quality scoring (0-2 scale)
- Test cases: 5 provided + 2 custom edge cases

## Development Timeline
- Data Collection: ~4 hours
- Model Training: ~6 hours
- Agent Development: ~8 hours
- Evaluation & Documentation: ~6 hours
- Total: ~24 hours

## License
This project is developed for Fenrir Security Private Limited internship evaluation.
All materials are confidential and proprietary.
