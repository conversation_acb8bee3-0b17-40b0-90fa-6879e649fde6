#!/usr/bin/env python3
"""
Data generation script for command-line Q&A pairs.
Generates ≥150 validated Q&A pairs covering Git, Bash, tar/gzip, grep, venv, etc.
"""

import json
import random
from typing import List, Dict

def generate_git_qa_pairs() -> List[Dict]:
    """Generate Git-related Q&A pairs."""
    git_pairs = [
        {
            "question": "How do I create a new Git repository?",
            "answer": "To create a new Git repository, use: git init\nThis initializes a new Git repository in the current directory.",
            "command": "git init",
            "category": "git"
        },
        {
            "question": "How do I clone a repository from GitHub?",
            "answer": "To clone a repository, use: git clone <repository-url>\nExample: git clone https://github.com/user/repo.git",
            "command": "git clone <repository-url>",
            "category": "git"
        },
        {
            "question": "How do I check the status of my Git repository?",
            "answer": "Use: git status\nThis shows the current state of your working directory and staging area.",
            "command": "git status",
            "category": "git"
        },
        {
            "question": "How do I add files to the staging area?",
            "answer": "To add files to staging area:\n- Add specific file: git add <filename>\n- Add all files: git add .\n- Add all modified files: git add -A",
            "command": "git add <filename>",
            "category": "git"
        },
        {
            "question": "How do I commit changes with a message?",
            "answer": "Use: git commit -m \"Your commit message\"\nThis commits staged changes with a descriptive message.",
            "command": "git commit -m \"message\"",
            "category": "git"
        },
        {
            "question": "How do I create a new branch?",
            "answer": "To create a new branch: git branch <branch-name>\nTo create and switch to it: git checkout -b <branch-name>",
            "command": "git checkout -b <branch-name>",
            "category": "git"
        },
        {
            "question": "How do I switch to a different branch?",
            "answer": "Use: git checkout <branch-name>\nOr with newer Git versions: git switch <branch-name>",
            "command": "git checkout <branch-name>",
            "category": "git"
        },
        {
            "question": "How do I merge a branch into main?",
            "answer": "First switch to main: git checkout main\nThen merge: git merge <branch-name>",
            "command": "git merge <branch-name>",
            "category": "git"
        },
        {
            "question": "How do I push changes to remote repository?",
            "answer": "Use: git push origin <branch-name>\nFor first push: git push -u origin <branch-name>",
            "command": "git push origin <branch-name>",
            "category": "git"
        },
        {
            "question": "How do I pull latest changes from remote?",
            "answer": "Use: git pull origin <branch-name>\nOr simply: git pull (if tracking branch is set)",
            "command": "git pull",
            "category": "git"
        },
        {
            "question": "How do I view commit history?",
            "answer": "Use: git log\nFor compact view: git log --oneline\nFor graph view: git log --graph --oneline",
            "command": "git log",
            "category": "git"
        },
        {
            "question": "How do I undo the last commit but keep changes?",
            "answer": "Use: git reset --soft HEAD~1\nThis undoes the commit but keeps changes staged.",
            "command": "git reset --soft HEAD~1",
            "category": "git"
        },
        {
            "question": "How do I see differences between working directory and last commit?",
            "answer": "Use: git diff\nFor staged changes: git diff --cached",
            "command": "git diff",
            "category": "git"
        },
        {
            "question": "How do I delete a branch?",
            "answer": "Delete local branch: git branch -d <branch-name>\nForce delete: git branch -D <branch-name>\nDelete remote branch: git push origin --delete <branch-name>",
            "command": "git branch -d <branch-name>",
            "category": "git"
        },
        {
            "question": "How do I stash my current changes?",
            "answer": "Use: git stash\nTo stash with message: git stash save \"message\"\nTo apply stash: git stash pop",
            "command": "git stash",
            "category": "git"
        }
    ]
    return git_pairs

def generate_bash_qa_pairs() -> List[Dict]:
    """Generate Bash-related Q&A pairs."""
    bash_pairs = [
        {
            "question": "How do I list all files in a directory?",
            "answer": "Use: ls\nFor detailed listing: ls -l\nInclude hidden files: ls -la",
            "command": "ls -la",
            "category": "bash"
        },
        {
            "question": "How do I change to a different directory?",
            "answer": "Use: cd <directory-path>\nTo go to home directory: cd ~\nTo go up one level: cd ..",
            "command": "cd <directory-path>",
            "category": "bash"
        },
        {
            "question": "How do I create a new directory?",
            "answer": "Use: mkdir <directory-name>\nTo create nested directories: mkdir -p path/to/directory",
            "command": "mkdir <directory-name>",
            "category": "bash"
        },
        {
            "question": "How do I copy files?",
            "answer": "Copy file: cp <source> <destination>\nCopy directory recursively: cp -r <source-dir> <dest-dir>",
            "command": "cp <source> <destination>",
            "category": "bash"
        },
        {
            "question": "How do I move or rename files?",
            "answer": "Use: mv <source> <destination>\nThis works for both moving and renaming files/directories.",
            "command": "mv <source> <destination>",
            "category": "bash"
        },
        {
            "question": "How do I delete files?",
            "answer": "Delete file: rm <filename>\nDelete directory: rm -r <directory>\nForce delete: rm -rf <path>",
            "command": "rm <filename>",
            "category": "bash"
        },
        {
            "question": "How do I view the contents of a file?",
            "answer": "Use: cat <filename>\nFor large files: less <filename> or more <filename>\nFirst few lines: head <filename>",
            "command": "cat <filename>",
            "category": "bash"
        },
        {
            "question": "How do I find my current directory?",
            "answer": "Use: pwd\nThis prints the current working directory path.",
            "command": "pwd",
            "category": "bash"
        },
        {
            "question": "How do I check disk usage?",
            "answer": "Use: df -h (filesystem usage)\nFor directory size: du -sh <directory>\nFor current directory: du -sh .",
            "command": "df -h",
            "category": "bash"
        },
        {
            "question": "How do I check running processes?",
            "answer": "Use: ps aux (all processes)\nFor interactive view: top or htop\nFind specific process: ps aux | grep <process-name>",
            "command": "ps aux",
            "category": "bash"
        },
        {
            "question": "How do I kill a process?",
            "answer": "Use: kill <process-id>\nForce kill: kill -9 <process-id>\nKill by name: killall <process-name>",
            "command": "kill <process-id>",
            "category": "bash"
        },
        {
            "question": "How do I check file permissions?",
            "answer": "Use: ls -l <filename>\nThe first column shows permissions (rwxrwxrwx format).",
            "command": "ls -l <filename>",
            "category": "bash"
        },
        {
            "question": "How do I change file permissions?",
            "answer": "Use: chmod <permissions> <filename>\nExample: chmod 755 script.sh\nOr: chmod +x script.sh (make executable)",
            "command": "chmod 755 <filename>",
            "category": "bash"
        },
        {
            "question": "How do I create an empty file?",
            "answer": "Use: touch <filename>\nThis creates an empty file or updates timestamp if file exists.",
            "command": "touch <filename>",
            "category": "bash"
        },
        {
            "question": "How do I find files by name?",
            "answer": "Use: find <directory> -name \"<pattern>\"\nExample: find . -name \"*.py\"\nCase insensitive: find . -iname \"*.txt\"",
            "command": "find . -name \"<pattern>\"",
            "category": "bash"
        }
    ]
    return bash_pairs

def generate_tar_gzip_qa_pairs() -> List[Dict]:
    """Generate tar/gzip-related Q&A pairs."""
    tar_pairs = [
        {
            "question": "How do I create a tar archive?",
            "answer": "Use: tar -cvf archive.tar files/\nWith compression: tar -czvf archive.tar.gz files/",
            "command": "tar -czvf archive.tar.gz files/",
            "category": "tar"
        },
        {
            "question": "How do I extract a tar archive?",
            "answer": "Use: tar -xvf archive.tar\nFor compressed: tar -xzvf archive.tar.gz",
            "command": "tar -xzvf archive.tar.gz",
            "category": "tar"
        },
        {
            "question": "How do I list contents of a tar file?",
            "answer": "Use: tar -tvf archive.tar\nFor compressed: tar -tzvf archive.tar.gz",
            "command": "tar -tzvf archive.tar.gz",
            "category": "tar"
        },
        {
            "question": "How do I compress a file with gzip?",
            "answer": "Use: gzip filename\nThis creates filename.gz and removes original.\nKeep original: gzip -k filename",
            "command": "gzip filename",
            "category": "gzip"
        },
        {
            "question": "How do I decompress a gzip file?",
            "answer": "Use: gunzip filename.gz\nOr: gzip -d filename.gz",
            "command": "gunzip filename.gz",
            "category": "gzip"
        }
    ]
    return tar_pairs

def generate_grep_qa_pairs() -> List[Dict]:
    """Generate grep-related Q&A pairs."""
    grep_pairs = [
        {
            "question": "How do I search for text in a file?",
            "answer": "Use: grep \"pattern\" filename\nCase insensitive: grep -i \"pattern\" filename",
            "command": "grep \"pattern\" filename",
            "category": "grep"
        },
        {
            "question": "How do I search recursively in directories?",
            "answer": "Use: grep -r \"pattern\" directory/\nInclude line numbers: grep -rn \"pattern\" directory/",
            "command": "grep -rn \"pattern\" directory/",
            "category": "grep"
        },
        {
            "question": "How do I search for whole words only?",
            "answer": "Use: grep -w \"word\" filename\nThis matches complete words, not partial matches.",
            "command": "grep -w \"word\" filename",
            "category": "grep"
        },
        {
            "question": "How do I count matching lines?",
            "answer": "Use: grep -c \"pattern\" filename\nThis returns the count of matching lines.",
            "command": "grep -c \"pattern\" filename",
            "category": "grep"
        },
        {
            "question": "How do I show lines before and after matches?",
            "answer": "Use: grep -A 3 -B 3 \"pattern\" filename\n-A: lines after, -B: lines before, -C: lines around",
            "command": "grep -A 3 -B 3 \"pattern\" filename",
            "category": "grep"
        }
    ]
    return grep_pairs

def generate_venv_qa_pairs() -> List[Dict]:
    """Generate virtual environment Q&A pairs."""
    venv_pairs = [
        {
            "question": "How do I create a Python virtual environment?",
            "answer": "Use: python -m venv myenv\nOr: python3 -m venv myenv\nThis creates a new virtual environment named 'myenv'.",
            "command": "python -m venv myenv",
            "category": "venv"
        },
        {
            "question": "How do I activate a virtual environment?",
            "answer": "On Linux/Mac: source myenv/bin/activate\nOn Windows: myenv\\Scripts\\activate",
            "command": "source myenv/bin/activate",
            "category": "venv"
        },
        {
            "question": "How do I deactivate a virtual environment?",
            "answer": "Simply use: deactivate\nThis works regardless of the operating system.",
            "command": "deactivate",
            "category": "venv"
        },
        {
            "question": "How do I install packages in a virtual environment?",
            "answer": "First activate the environment, then: pip install package-name\nInstall from requirements: pip install -r requirements.txt",
            "command": "pip install package-name",
            "category": "venv"
        },
        {
            "question": "How do I list installed packages in virtual environment?",
            "answer": "Use: pip list\nOr for requirements format: pip freeze\nSave to file: pip freeze > requirements.txt",
            "command": "pip freeze",
            "category": "venv"
        }
    ]
    return venv_pairs

def generate_ssh_qa_pairs() -> List[Dict]:
    """Generate SSH-related Q&A pairs."""
    ssh_pairs = [
        {
            "question": "How do I connect to a remote server via SSH?",
            "answer": "Use: ssh username@hostname\nWith specific port: ssh -p 2222 username@hostname",
            "command": "ssh username@hostname",
            "category": "ssh"
        },
        {
            "question": "How do I copy files over SSH?",
            "answer": "Use: scp file.txt username@hostname:/path/\nCopy directory: scp -r directory/ username@hostname:/path/",
            "command": "scp file.txt username@hostname:/path/",
            "category": "ssh"
        },
        {
            "question": "How do I generate SSH keys?",
            "answer": "Use: ssh-keygen -t rsa -b 4096\nFor Ed25519: ssh-keygen -t ed25519",
            "command": "ssh-keygen -t rsa -b 4096",
            "category": "ssh"
        }
    ]
    return ssh_pairs

def generate_docker_qa_pairs() -> List[Dict]:
    """Generate Docker-related Q&A pairs."""
    docker_pairs = [
        {
            "question": "How do I list Docker containers?",
            "answer": "Use: docker ps (running containers)\nAll containers: docker ps -a",
            "command": "docker ps -a",
            "category": "docker"
        },
        {
            "question": "How do I run a Docker container?",
            "answer": "Use: docker run image-name\nInteractive mode: docker run -it image-name\nDetached mode: docker run -d image-name",
            "command": "docker run -it image-name",
            "category": "docker"
        },
        {
            "question": "How do I stop a Docker container?",
            "answer": "Use: docker stop container-id\nOr: docker stop container-name",
            "command": "docker stop container-id",
            "category": "docker"
        },
        {
            "question": "How do I build a Docker image?",
            "answer": "Use: docker build -t image-name .\nWith specific Dockerfile: docker build -f Dockerfile.dev -t image-name .",
            "command": "docker build -t image-name .",
            "category": "docker"
        }
    ]
    return docker_pairs

def generate_network_qa_pairs() -> List[Dict]:
    """Generate network-related Q&A pairs."""
    network_pairs = [
        {
            "question": "How do I check network connectivity?",
            "answer": "Use: ping hostname\nExample: ping google.com\nLimit packets: ping -c 4 hostname",
            "command": "ping google.com",
            "category": "network"
        },
        {
            "question": "How do I check open ports?",
            "answer": "Use: netstat -tuln\nOr: ss -tuln\nFor specific port: netstat -tuln | grep :80",
            "command": "netstat -tuln",
            "category": "network"
        },
        {
            "question": "How do I download a file from URL?",
            "answer": "Use: wget URL\nOr: curl -O URL\nWith custom name: curl -o filename URL",
            "command": "wget URL",
            "category": "network"
        }
    ]
    return network_pairs

def generate_text_processing_qa_pairs() -> List[Dict]:
    """Generate text processing Q&A pairs."""
    text_pairs = [
        {
            "question": "How do I count lines in a file?",
            "answer": "Use: wc -l filename\nCount words: wc -w filename\nCount characters: wc -c filename",
            "command": "wc -l filename",
            "category": "text"
        },
        {
            "question": "How do I sort lines in a file?",
            "answer": "Use: sort filename\nReverse order: sort -r filename\nNumeric sort: sort -n filename",
            "command": "sort filename",
            "category": "text"
        },
        {
            "question": "How do I remove duplicate lines?",
            "answer": "Use: sort filename | uniq\nOr: sort -u filename\nCount duplicates: sort filename | uniq -c",
            "command": "sort filename | uniq",
            "category": "text"
        },
        {
            "question": "How do I replace text in a file?",
            "answer": "Use: sed 's/old/new/g' filename\nIn-place edit: sed -i 's/old/new/g' filename",
            "command": "sed 's/old/new/g' filename",
            "category": "text"
        },
        {
            "question": "How do I view first 10 lines of a file?",
            "answer": "Use: head filename\nCustom number: head -n 20 filename",
            "command": "head filename",
            "category": "text"
        },
        {
            "question": "How do I view last 10 lines of a file?",
            "answer": "Use: tail filename\nFollow file changes: tail -f filename",
            "command": "tail filename",
            "category": "text"
        },
        {
            "question": "How do I cut specific columns from text?",
            "answer": "Use: cut -d',' -f1,3 filename\n-d: delimiter, -f: fields",
            "command": "cut -d',' -f1,3 filename",
            "category": "text"
        }
    ]
    return text_pairs

def generate_advanced_git_qa_pairs() -> List[Dict]:
    """Generate more advanced Git Q&A pairs."""
    advanced_git = [
        {
            "question": "How do I rebase my branch onto main?",
            "answer": "Use: git rebase main\nInteractive rebase: git rebase -i main",
            "command": "git rebase main",
            "category": "git"
        },
        {
            "question": "How do I cherry-pick a commit?",
            "answer": "Use: git cherry-pick <commit-hash>\nMultiple commits: git cherry-pick <hash1> <hash2>",
            "command": "git cherry-pick <commit-hash>",
            "category": "git"
        },
        {
            "question": "How do I create a Git tag?",
            "answer": "Use: git tag v1.0.0\nAnnotated tag: git tag -a v1.0.0 -m \"Version 1.0.0\"",
            "command": "git tag -a v1.0.0 -m \"Version 1.0.0\"",
            "category": "git"
        },
        {
            "question": "How do I configure Git user settings?",
            "answer": "Use: git config --global user.name \"Your Name\"\ngit config --global user.email \"<EMAIL>\"",
            "command": "git config --global user.name \"Your Name\"",
            "category": "git"
        },
        {
            "question": "How do I see Git configuration?",
            "answer": "Use: git config --list\nSpecific setting: git config user.name",
            "command": "git config --list",
            "category": "git"
        }
    ]
    return advanced_git

def generate_system_qa_pairs() -> List[Dict]:
    """Generate system administration Q&A pairs."""
    system_pairs = [
        {
            "question": "How do I check system memory usage?",
            "answer": "Use: free -h\nDetailed info: cat /proc/meminfo",
            "command": "free -h",
            "category": "system"
        },
        {
            "question": "How do I check CPU information?",
            "answer": "Use: lscpu\nOr: cat /proc/cpuinfo",
            "command": "lscpu",
            "category": "system"
        },
        {
            "question": "How do I monitor system resources?",
            "answer": "Use: top\nOr: htop (if installed)\nOne-time snapshot: ps aux",
            "command": "top",
            "category": "system"
        },
        {
            "question": "How do I check system uptime?",
            "answer": "Use: uptime\nThis shows how long system has been running and load averages.",
            "command": "uptime",
            "category": "system"
        },
        {
            "question": "How do I check mounted filesystems?",
            "answer": "Use: mount\nOr: df -h (with usage info)",
            "command": "mount",
            "category": "system"
        },
        {
            "question": "How do I change file ownership?",
            "answer": "Use: chown user:group filename\nRecursive: chown -R user:group directory/",
            "command": "chown user:group filename",
            "category": "system"
        },
        {
            "question": "How do I check environment variables?",
            "answer": "Use: env\nSpecific variable: echo $VARIABLE_NAME\nSet variable: export VAR=value",
            "command": "env",
            "category": "system"
        }
    ]
    return system_pairs

def generate_package_management_qa_pairs() -> List[Dict]:
    """Generate package management Q&A pairs."""
    package_pairs = [
        {
            "question": "How do I update package lists on Ubuntu?",
            "answer": "Use: sudo apt update\nThen upgrade: sudo apt upgrade",
            "command": "sudo apt update",
            "category": "package"
        },
        {
            "question": "How do I install a package on Ubuntu?",
            "answer": "Use: sudo apt install package-name\nMultiple packages: sudo apt install pkg1 pkg2",
            "command": "sudo apt install package-name",
            "category": "package"
        },
        {
            "question": "How do I search for packages on Ubuntu?",
            "answer": "Use: apt search keyword\nShow package info: apt show package-name",
            "command": "apt search keyword",
            "category": "package"
        },
        {
            "question": "How do I remove a package on Ubuntu?",
            "answer": "Use: sudo apt remove package-name\nWith config files: sudo apt purge package-name",
            "command": "sudo apt remove package-name",
            "category": "package"
        },
        {
            "question": "How do I install packages on CentOS/RHEL?",
            "answer": "Use: sudo yum install package-name\nOr newer: sudo dnf install package-name",
            "command": "sudo yum install package-name",
            "category": "package"
        }
    ]
    return package_pairs

def generate_file_operations_qa_pairs() -> List[Dict]:
    """Generate more file operation Q&A pairs."""
    file_pairs = [
        {
            "question": "How do I create a symbolic link?",
            "answer": "Use: ln -s target linkname\nHard link: ln target linkname",
            "command": "ln -s target linkname",
            "category": "files"
        },
        {
            "question": "How do I find large files?",
            "answer": "Use: find / -size +100M\nTop 10 largest: du -ah / | sort -rh | head -10",
            "command": "find / -size +100M",
            "category": "files"
        },
        {
            "question": "How do I compare two files?",
            "answer": "Use: diff file1 file2\nSide by side: diff -y file1 file2",
            "command": "diff file1 file2",
            "category": "files"
        },
        {
            "question": "How do I create a backup of a file?",
            "answer": "Use: cp file.txt file.txt.bak\nWith timestamp: cp file.txt file.txt.$(date +%Y%m%d)",
            "command": "cp file.txt file.txt.bak",
            "category": "files"
        },
        {
            "question": "How do I find files modified in last 7 days?",
            "answer": "Use: find . -mtime -7\nLast 24 hours: find . -mtime -1",
            "command": "find . -mtime -7",
            "category": "files"
        }
    ]
    return file_pairs

def generate_scripting_qa_pairs() -> List[Dict]:
    """Generate shell scripting Q&A pairs."""
    scripting_pairs = [
        {
            "question": "How do I make a shell script executable?",
            "answer": "Use: chmod +x script.sh\nThen run: ./script.sh",
            "command": "chmod +x script.sh",
            "category": "scripting"
        },
        {
            "question": "How do I create a simple bash script?",
            "answer": "Create file with #!/bin/bash at top\nExample:\n#!/bin/bash\necho \"Hello World\"",
            "command": "#!/bin/bash",
            "category": "scripting"
        },
        {
            "question": "How do I pass arguments to a script?",
            "answer": "Use $1, $2, etc. for arguments\n$0 is script name, $# is argument count",
            "command": "echo $1 $2",
            "category": "scripting"
        },
        {
            "question": "How do I create a for loop in bash?",
            "answer": "Use: for i in {1..10}; do echo $i; done\nOr: for file in *.txt; do echo $file; done",
            "command": "for i in {1..10}; do echo $i; done",
            "category": "scripting"
        },
        {
            "question": "How do I check if a file exists in bash?",
            "answer": "Use: if [ -f \"file.txt\" ]; then echo \"exists\"; fi\n-d for directory, -e for any type",
            "command": "if [ -f \"file.txt\" ]; then echo \"exists\"; fi",
            "category": "scripting"
        }
    ]
    return scripting_pairs

def generate_database_qa_pairs() -> List[Dict]:
    """Generate database-related command line Q&A pairs."""
    db_pairs = [
        {
            "question": "How do I connect to MySQL from command line?",
            "answer": "Use: mysql -u username -p\nSpecific database: mysql -u username -p database_name",
            "command": "mysql -u username -p",
            "category": "database"
        },
        {
            "question": "How do I backup a MySQL database?",
            "answer": "Use: mysqldump -u username -p database_name > backup.sql",
            "command": "mysqldump -u username -p database_name > backup.sql",
            "category": "database"
        },
        {
            "question": "How do I restore a MySQL database?",
            "answer": "Use: mysql -u username -p database_name < backup.sql",
            "command": "mysql -u username -p database_name < backup.sql",
            "category": "database"
        },
        {
            "question": "How do I connect to PostgreSQL?",
            "answer": "Use: psql -U username -d database_name\nLocal connection: psql database_name",
            "command": "psql -U username -d database_name",
            "category": "database"
        }
    ]
    return db_pairs

def generate_monitoring_qa_pairs() -> List[Dict]:
    """Generate system monitoring Q&A pairs."""
    monitoring_pairs = [
        {
            "question": "How do I monitor disk I/O?",
            "answer": "Use: iostat\nContinuous monitoring: iostat 2\nDetailed: iotop (if installed)",
            "command": "iostat",
            "category": "monitoring"
        },
        {
            "question": "How do I check network connections?",
            "answer": "Use: netstat -an\nActive connections: netstat -ant\nListening ports: netstat -tln",
            "command": "netstat -an",
            "category": "monitoring"
        },
        {
            "question": "How do I monitor log files in real-time?",
            "answer": "Use: tail -f /var/log/syslog\nMultiple files: tail -f file1 file2",
            "command": "tail -f /var/log/syslog",
            "category": "monitoring"
        },
        {
            "question": "How do I check system load?",
            "answer": "Use: uptime\nDetailed: w\nLoad averages show 1, 5, 15 minute averages",
            "command": "uptime",
            "category": "monitoring"
        }
    ]
    return monitoring_pairs

def generate_security_qa_pairs() -> List[Dict]:
    """Generate security-related Q&A pairs."""
    security_pairs = [
        {
            "question": "How do I check user login history?",
            "answer": "Use: last\nFailed logins: lastb\nCurrent users: who",
            "command": "last",
            "category": "security"
        },
        {
            "question": "How do I change user password?",
            "answer": "Use: passwd\nFor other user: sudo passwd username",
            "command": "passwd",
            "category": "security"
        },
        {
            "question": "How do I check sudo privileges?",
            "answer": "Use: sudo -l\nThis lists what commands you can run with sudo",
            "command": "sudo -l",
            "category": "security"
        },
        {
            "question": "How do I generate a secure password?",
            "answer": "Use: openssl rand -base64 32\nOr: pwgen 16 1 (if installed)",
            "command": "openssl rand -base64 32",
            "category": "security"
        }
    ]
    return security_pairs

def generate_development_qa_pairs() -> List[Dict]:
    """Generate development-related Q&A pairs."""
    dev_pairs = [
        {
            "question": "How do I check Python version?",
            "answer": "Use: python --version\nOr: python3 --version",
            "command": "python --version",
            "category": "development"
        },
        {
            "question": "How do I install Node.js packages?",
            "answer": "Use: npm install package-name\nGlobally: npm install -g package-name\nFrom package.json: npm install",
            "command": "npm install package-name",
            "category": "development"
        },
        {
            "question": "How do I run a Python HTTP server?",
            "answer": "Use: python -m http.server 8000\nPython 2: python -m SimpleHTTPServer 8000",
            "command": "python -m http.server 8000",
            "category": "development"
        },
        {
            "question": "How do I check which ports are in use?",
            "answer": "Use: lsof -i :8080\nAll ports: lsof -i\nSpecific protocol: lsof -i tcp",
            "command": "lsof -i :8080",
            "category": "development"
        },
        {
            "question": "How do I compile C code?",
            "answer": "Use: gcc -o program program.c\nWith debugging: gcc -g -o program program.c",
            "command": "gcc -o program program.c",
            "category": "development"
        }
    ]
    return dev_pairs

def generate_advanced_bash_qa_pairs() -> List[Dict]:
    """Generate advanced bash Q&A pairs."""
    advanced_bash = [
        {
            "question": "How do I redirect output to a file?",
            "answer": "Use: command > file.txt\nAppend: command >> file.txt\nBoth stdout and stderr: command &> file.txt",
            "command": "command > file.txt",
            "category": "bash"
        },
        {
            "question": "How do I pipe output between commands?",
            "answer": "Use: command1 | command2\nExample: ls -la | grep \".txt\"",
            "command": "ls -la | grep \".txt\"",
            "category": "bash"
        },
        {
            "question": "How do I run commands in background?",
            "answer": "Use: command &\nBring to foreground: fg\nList jobs: jobs",
            "command": "command &",
            "category": "bash"
        },
        {
            "question": "How do I create command aliases?",
            "answer": "Use: alias ll='ls -la'\nPermanent: add to ~/.bashrc\nRemove alias: unalias ll",
            "command": "alias ll='ls -la'",
            "category": "bash"
        },
        {
            "question": "How do I use command history?",
            "answer": "Use: history\nRun previous: !!\nRun command by number: !123\nSearch: Ctrl+R",
            "command": "history",
            "category": "bash"
        }
    ]
    return advanced_bash

def generate_additional_qa_pairs() -> List[Dict]:
    """Generate additional Q&A pairs to reach 150+ total."""
    additional_pairs = [
        # More Git operations
        {
            "question": "How do I undo changes in working directory?",
            "answer": "Use: git checkout -- filename\nAll files: git checkout -- .\nOr: git restore filename",
            "command": "git checkout -- filename",
            "category": "git"
        },
        {
            "question": "How do I see what changed in a commit?",
            "answer": "Use: git show <commit-hash>\nJust file names: git show --name-only <commit-hash>",
            "command": "git show <commit-hash>",
            "category": "git"
        },
        {
            "question": "How do I create a Git alias?",
            "answer": "Use: git config --global alias.st status\nThen use: git st",
            "command": "git config --global alias.st status",
            "category": "git"
        },
        # More file operations
        {
            "question": "How do I find files by type?",
            "answer": "Use: find . -type f -name \"*.py\"\n-type d for directories, -type l for links",
            "command": "find . -type f -name \"*.py\"",
            "category": "files"
        },
        {
            "question": "How do I calculate file checksums?",
            "answer": "Use: md5sum filename\nSHA256: sha256sum filename\nVerify: md5sum -c checksums.txt",
            "command": "md5sum filename",
            "category": "files"
        },
        {
            "question": "How do I split a large file?",
            "answer": "Use: split -b 100M largefile.txt\nBy lines: split -l 1000 file.txt",
            "command": "split -b 100M largefile.txt",
            "category": "files"
        },
        # More text processing
        {
            "question": "How do I extract specific lines from a file?",
            "answer": "Use: sed -n '10,20p' filename\nSingle line: sed -n '15p' filename",
            "command": "sed -n '10,20p' filename",
            "category": "text"
        },
        {
            "question": "How do I join files horizontally?",
            "answer": "Use: paste file1.txt file2.txt\nWith delimiter: paste -d',' file1.txt file2.txt",
            "command": "paste file1.txt file2.txt",
            "category": "text"
        },
        {
            "question": "How do I convert text case?",
            "answer": "Use: tr '[:lower:]' '[:upper:]' < file.txt\nReverse: tr '[:upper:]' '[:lower:]'",
            "command": "tr '[:lower:]' '[:upper:]' < file.txt",
            "category": "text"
        },
        # More system operations
        {
            "question": "How do I schedule a task with cron?",
            "answer": "Use: crontab -e\nFormat: minute hour day month weekday command\nExample: 0 2 * * * /path/to/script.sh",
            "command": "crontab -e",
            "category": "system"
        },
        {
            "question": "How do I check system logs?",
            "answer": "Use: journalctl\nFollow logs: journalctl -f\nSpecific service: journalctl -u service-name",
            "command": "journalctl",
            "category": "system"
        },
        {
            "question": "How do I manage services?",
            "answer": "Use: systemctl status service-name\nStart: systemctl start service\nStop: systemctl stop service",
            "command": "systemctl status service-name",
            "category": "system"
        },
        # More network operations
        {
            "question": "How do I test network speed?",
            "answer": "Use: speedtest-cli (if installed)\nOr: curl -s https://raw.githubusercontent.com/sivel/speedtest-cli/master/speedtest.py | python -",
            "command": "speedtest-cli",
            "category": "network"
        },
        {
            "question": "How do I trace network route?",
            "answer": "Use: traceroute hostname\nOn Windows: tracert hostname",
            "command": "traceroute hostname",
            "category": "network"
        },
        {
            "question": "How do I check DNS resolution?",
            "answer": "Use: nslookup hostname\nOr: dig hostname\nReverse lookup: dig -x IP_ADDRESS",
            "command": "nslookup hostname",
            "category": "network"
        },
        # More development tools
        {
            "question": "How do I format JSON in command line?",
            "answer": "Use: cat file.json | python -m json.tool\nOr: jq '.' file.json (if installed)",
            "command": "cat file.json | python -m json.tool",
            "category": "development"
        },
        {
            "question": "How do I encode/decode base64?",
            "answer": "Encode: echo \"text\" | base64\nDecode: echo \"dGV4dA==\" | base64 -d",
            "command": "echo \"text\" | base64",
            "category": "development"
        },
        {
            "question": "How do I generate UUID?",
            "answer": "Use: uuidgen\nOr: python -c \"import uuid; print(uuid.uuid4())\"",
            "command": "uuidgen",
            "category": "development"
        },
        # More Docker operations
        {
            "question": "How do I remove Docker containers?",
            "answer": "Use: docker rm container-id\nAll stopped: docker container prune\nForce remove: docker rm -f container-id",
            "command": "docker rm container-id",
            "category": "docker"
        },
        {
            "question": "How do I view Docker logs?",
            "answer": "Use: docker logs container-id\nFollow logs: docker logs -f container-id",
            "command": "docker logs container-id",
            "category": "docker"
        },
        {
            "question": "How do I execute commands in Docker container?",
            "answer": "Use: docker exec -it container-id bash\nSingle command: docker exec container-id ls /",
            "command": "docker exec -it container-id bash",
            "category": "docker"
        },
        # More archive operations
        {
            "question": "How do I create a zip archive?",
            "answer": "Use: zip -r archive.zip directory/\nExclude files: zip -r archive.zip dir/ -x \"*.log\"",
            "command": "zip -r archive.zip directory/",
            "category": "archive"
        },
        {
            "question": "How do I extract a zip file?",
            "answer": "Use: unzip archive.zip\nTo specific directory: unzip archive.zip -d /path/",
            "command": "unzip archive.zip",
            "category": "archive"
        },
        # More process management
        {
            "question": "How do I run a command with timeout?",
            "answer": "Use: timeout 30s command\nKill after timeout: timeout -k 5s 30s command",
            "command": "timeout 30s command",
            "category": "process"
        },
        {
            "question": "How do I run command with different priority?",
            "answer": "Use: nice -n 10 command\nLower priority: nice -n 19 command\nHigher: nice -n -10 command",
            "command": "nice -n 10 command",
            "category": "process"
        },
        # More search operations
        {
            "question": "How do I search for text in multiple files?",
            "answer": "Use: grep -r \"pattern\" .\nSpecific file types: grep -r --include=\"*.py\" \"pattern\" .",
            "command": "grep -r \"pattern\" .",
            "category": "search"
        },
        {
            "question": "How do I find and replace in multiple files?",
            "answer": "Use: find . -name \"*.txt\" -exec sed -i 's/old/new/g' {} \\;\nOr: grep -rl \"old\" . | xargs sed -i 's/old/new/g'",
            "command": "find . -name \"*.txt\" -exec sed -i 's/old/new/g' {} \\;",
            "category": "search"
        },
        # Final pairs to reach 150+
        {
            "question": "How do I create a hard link?",
            "answer": "Use: ln source_file hard_link\nUnlike symbolic links, hard links point directly to file data.",
            "command": "ln source_file hard_link",
            "category": "files"
        },
        {
            "question": "How do I check file type?",
            "answer": "Use: file filename\nThis identifies file type based on content, not extension.",
            "command": "file filename",
            "category": "files"
        },
        {
            "question": "How do I create a RAM disk?",
            "answer": "Use: sudo mount -t tmpfs -o size=1G tmpfs /mnt/ramdisk\nTemporary filesystem in memory.",
            "command": "sudo mount -t tmpfs -o size=1G tmpfs /mnt/ramdisk",
            "category": "system"
        },
        {
            "question": "How do I check which shell I'm using?",
            "answer": "Use: echo $SHELL\nOr: ps -p $$\nCurrent process: echo $0",
            "command": "echo $SHELL",
            "category": "bash"
        },
        {
            "question": "How do I create a function in bash?",
            "answer": "Use: function_name() { commands; }\nExample: hello() { echo \"Hello $1\"; }",
            "command": "hello() { echo \"Hello $1\"; }",
            "category": "scripting"
        },
        {
            "question": "How do I monitor bandwidth usage?",
            "answer": "Use: iftop (if installed)\nOr: nethogs\nBasic: cat /proc/net/dev",
            "command": "iftop",
            "category": "monitoring"
        },
        {
            "question": "How do I compress files with maximum compression?",
            "answer": "Use: gzip -9 filename\nOr: xz filename (better compression)\nBzip2: bzip2 filename",
            "command": "gzip -9 filename",
            "category": "archive"
        },
        {
            "question": "How do I find the largest directories?",
            "answer": "Use: du -h --max-depth=1 | sort -hr\nTop 10: du -h --max-depth=1 | sort -hr | head -10",
            "command": "du -h --max-depth=1 | sort -hr",
            "category": "files"
        },
        {
            "question": "How do I create a secure tunnel with SSH?",
            "answer": "Use: ssh -L 8080:localhost:80 user@server\nReverse tunnel: ssh -R 8080:localhost:80 user@server",
            "command": "ssh -L 8080:localhost:80 user@server",
            "category": "ssh"
        },
        {
            "question": "How do I run a command every few seconds?",
            "answer": "Use: watch -n 2 command\nHighlight changes: watch -d command",
            "command": "watch -n 2 command",
            "category": "monitoring"
        },
        {
            "question": "How do I check what's using a specific port?",
            "answer": "Use: lsof -i :8080\nOr: netstat -tulpn | grep :8080\nFuser: fuser 8080/tcp",
            "command": "lsof -i :8080",
            "category": "network"
        },
        {
            "question": "How do I create a backup with rsync?",
            "answer": "Use: rsync -av source/ destination/\nWith progress: rsync -av --progress source/ dest/",
            "command": "rsync -av source/ destination/",
            "category": "backup"
        },
        {
            "question": "How do I exclude files in rsync?",
            "answer": "Use: rsync -av --exclude='*.log' source/ dest/\nMultiple: rsync -av --exclude='*.log' --exclude='tmp/' source/ dest/",
            "command": "rsync -av --exclude='*.log' source/ dest/",
            "category": "backup"
        },
        {
            "question": "How do I check system temperature?",
            "answer": "Use: sensors (if installed)\nOr: cat /sys/class/thermal/thermal_zone*/temp",
            "command": "sensors",
            "category": "monitoring"
        },
        {
            "question": "How do I create a bootable USB?",
            "answer": "Use: dd if=image.iso of=/dev/sdX bs=4M status=progress\nReplace sdX with your USB device.",
            "command": "dd if=image.iso of=/dev/sdX bs=4M status=progress",
            "category": "system"
        }
    ]
    return additional_pairs

if __name__ == "__main__":
    # Generate all Q&A pairs
    all_pairs = []
    all_pairs.extend(generate_git_qa_pairs())
    all_pairs.extend(generate_bash_qa_pairs())
    all_pairs.extend(generate_tar_gzip_qa_pairs())
    all_pairs.extend(generate_grep_qa_pairs())
    all_pairs.extend(generate_venv_qa_pairs())
    all_pairs.extend(generate_ssh_qa_pairs())
    all_pairs.extend(generate_docker_qa_pairs())
    all_pairs.extend(generate_network_qa_pairs())
    all_pairs.extend(generate_text_processing_qa_pairs())
    all_pairs.extend(generate_advanced_git_qa_pairs())
    all_pairs.extend(generate_system_qa_pairs())
    all_pairs.extend(generate_package_management_qa_pairs())
    all_pairs.extend(generate_file_operations_qa_pairs())
    all_pairs.extend(generate_scripting_qa_pairs())
    all_pairs.extend(generate_database_qa_pairs())
    all_pairs.extend(generate_monitoring_qa_pairs())
    all_pairs.extend(generate_security_qa_pairs())
    all_pairs.extend(generate_development_qa_pairs())
    all_pairs.extend(generate_advanced_bash_qa_pairs())
    all_pairs.extend(generate_additional_qa_pairs())

    print(f"Generated {len(all_pairs)} Q&A pairs total")

    # Add some metadata
    dataset = {
        "metadata": {
            "total_pairs": len(all_pairs),
            "categories": list(set(pair["category"] for pair in all_pairs)),
            "created_date": "2025-06-17",
            "purpose": "Command-line assistant training data"
        },
        "data": all_pairs
    }

    # Save to JSON file
    with open('command_line_qa.json', 'w', encoding='utf-8') as f:
        json.dump(dataset, f, indent=2, ensure_ascii=False)

    print(f"Saved {len(all_pairs)} Q&A pairs to command_line_qa.json")
    print(f"Categories: {dataset['metadata']['categories']}")
